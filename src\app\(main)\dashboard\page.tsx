
"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { AppLogo } from "@/components/AppLogo";
import { FilePlus, BarChart3, Archive, SettingsIcon as Settings, Search, LogOut } from "lucide-react";
import { useArchiveStore } from "@/store/archiveStore";
import React, { useState, useEffect } from "react";
import { IntroAnimation } from "@/components/shared/IntroAnimation";

export default function DashboardPage() {
  const { setAppState } = useArchiveStore();
  const [showIntro, setShowIntro] = useState(false);

  useEffect(() => {
    const introPlayedInSession = sessionStorage.getItem('introPlayed');
    if (!introPlayedInSession) {
      setShowIntro(true);
    }
  }, []);

  const handleIntroComplete = () => {
    sessionStorage.setItem('introPlayed', 'true');
    setShowIntro(false);
  };

  if (showIntro) {
    return <IntroAnimation onAnimationComplete={handleIntroComplete} />;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-full text-center p-4 md:p-8 dashboard-bg">
      <Card className="w-full max-w-7xl shadow-xl border-2 border-primary/30 bg-card/80 backdrop-blur-sm">
        <CardHeader className="items-center pb-4">
          <AppLogo className="w-20 h-20 text-primary mb-4" />
          <div className="page-header-title-bg my-2">
            <CardTitle className="text-5xl font-headline mb-2 page-header-title-text">
              برنامج الأرشفة الإلكترونية
            </CardTitle>
          </div>
          <CardDescription className="text-xl text-foreground/80">
            مرحباً بك في سفينة الأرشيف! نظامك المتكامل لإدارة وأرشفة المستندات بكفاءة وسهولة.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8 pt-4">
          <p className="text-lg text-muted-foreground">
            ابدأ بتنظيم ملفاتك، أو استعرض أرشيفك، أو قم بتخصيص إعدادات البرنامج. اختر من الخيارات أدناه:
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <Link href="/data-entry" passHref>
              <Button variant="default" size="lg" className="w-full py-8 text-lg glowing-button">
                <FilePlus className="ml-3 h-7 w-7" />
                إضافة قيد جديد
              </Button>
            </Link>
            <Link href="/archive" passHref>
              <Button variant="secondary" size="lg" className="w-full py-8 text-lg">
                <Archive className="ml-3 h-7 w-7" />
                استعراض الأرشيف
              </Button>
            </Link>
            <Link href="/reports" passHref>
              <Button variant="outline" size="lg" className="w-full py-8 text-lg">
                <BarChart3 className="ml-3 h-7 w-7" />
                عرض التقارير
              </Button>
            </Link>
            <Link href="/filter" passHref>
              <Button variant="outline" size="lg" className="w-full py-8 text-lg">
                <Search className="ml-3 h-7 w-7" />
                التصفية المتقدمة
              </Button>
            </Link>
            <Link href="/settings" passHref>
              <Button variant="ghost" size="lg" className="w-full py-8 text-lg">
                <Settings className="ml-3 h-7 w-7" />
                الإعدادات
              </Button>
            </Link>
            <Button 
              variant="destructive" 
              size="lg" 
              className="w-full py-8 text-lg col-span-1 sm:col-span-2 lg:col-span-3 glowing-button"
              onClick={() => setAppState('exiting')}
            >
              <LogOut className="ml-3 h-7 w-7" />
              الخروج من البرنامج
            </Button>
          </div>
        </CardContent>
      </Card>
      <footer className="mt-12 text-md text-foreground/70">
        <p className="font-semibold">إعداد: المحاسب المبرمج علي عاجل خشان المحنة</p>
        <p>الإصدار: 2.0.0</p>
        <p>&copy; {new Date().getFullYear()} جميع الحقوق محفوظة.</p>
      </footer>
    </div>
  );
}
