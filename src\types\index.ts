
export interface ArchiveEntry {
  id: string;
  documentNumber: string;
  documentDate: Date;
  subject: string;
  contentEmployeeName: string;
  department: string;
  marginNotes: string;
}

export type Theme = 'light' | 'dark' | 'theme-ocean-sunset' | 'theme-fiery-passion' | 'theme-electric-pop';

export interface Settings {
  theme: Theme;
}

// New type for app state
export type AppStateValue = 'running' | 'exiting';

export interface ArchiveState {
  entries: ArchiveEntry[];
  settings: Settings;
  appState: AppStateValue; // Added appState
  addEntry: (entry: Omit<ArchiveEntry, 'id'>) => void;
  updateEntry: (id: string, updatedEntry: Partial<ArchiveEntry>) => void;
  deleteEntry: (id: string) => void;
  setTheme: (theme: Theme) => void;
  setAppState: (newState: AppStateValue) => void; // Added setAppState
  getEntryById: (id: string) => ArchiveEntry | undefined;
}
