"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[239],{1414:(e,t,n)=>{n.d(t,{c:()=>u});var r=n(2115);function u(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},2712:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(2115),u=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>s});var r=n(2115),u=n(7650),i=n(9708),o=n(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...u}=e,s=r?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...u,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},4378:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2115),u=n(7650),i=n(3655),o=n(2712),s=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:a,...d}=e,[c,f]=r.useState(!1);(0,o.N)(()=>f(!0),[]);let v=a||c&&(null===(l=globalThis)||void 0===l?void 0:null===(n=l.document)||void 0===n?void 0:n.body);return v?u.createPortal((0,s.jsx)(i.sG.div,{...d,ref:t}),v):null});l.displayName="Portal"},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(2115),u=n(1414);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,o]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,o=r.useRef(i),s=(0,u.c)(t);return r.useEffect(()=>{o.current!==i&&(s(i),o.current=i)},[i,o,s]),n}({defaultProp:t,onChange:n}),s=void 0!==e,l=s?e:i,a=(0,u.c)(n);return[l,r.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a(n)}else o(t)},[s,e,o,a])]}},6081:(e,t,n)=>{n.d(t,{A:()=>o,q:()=>i});var r=n(2115),u=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,o=r.useMemo(()=>i,Object.values(i));return(0,u.jsx)(n.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(u){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${u}\` must be used within \`${e}\``)}]}function o(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let u=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:u}}),[n,u])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),s=n.length;n=[...n,i];let l=t=>{let{scope:n,children:i,...l}=t,a=n?.[e]?.[s]||o,d=r.useMemo(()=>l,Object.values(l));return(0,u.jsx)(a.Provider,{value:d,children:i})};return l.displayName=t+"Provider",[l,function(n,u){let l=u?.[e]?.[s]||o,a=r.useContext(l);if(a)return a;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let u=n.reduce((t,{useScope:n,scopeName:r})=>{let u=n(e)[`__scope${r}`];return{...t,...u}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return n.scopeName=t.scopeName,n}(i,...t)]}},8905:(e,t,n)=>{n.d(t,{C:()=>o});var r=n(2115),u=n(6101),i=n(2712),o=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[u,o]=r.useState(),l=r.useRef({}),a=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(l.current);d.current="mounted"===c?e:"none"},[c]),(0,i.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=d.current,u=s(t);e?f("MOUNT"):"none"===u||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==u?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,i.N)(()=>{if(u){var e;let t;let n=null!==(e=u.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=s(l.current).includes(e.animationName);if(e.target===u&&r&&(f("ANIMATION_END"),!a.current)){let e=u.style.animationFillMode;u.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===u.style.animationFillMode&&(u.style.animationFillMode=e)})}},i=e=>{e.target===u&&(d.current=s(l.current))};return u.addEventListener("animationstart",i),u.addEventListener("animationcancel",r),u.addEventListener("animationend",r),()=>{n.clearTimeout(t),u.removeEventListener("animationstart",i),u.removeEventListener("animationcancel",r),u.removeEventListener("animationend",r)}}f("ANIMATION_END")},[u,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),l="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),a=(0,u.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,u=r&&"isReactWarning"in r&&r.isReactWarning;return u?e.ref:(u=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||o.isPresent?r.cloneElement(l,{ref:a}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},9178:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>f,bL:()=>E});var r,u=n(2115),i=n(5185),o=n(3655),s=n(6101),l=n(1414),a=n(5155),d="dismissableLayer.update",c=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=u.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:E,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:N,onDismiss:h,...w}=e,g=u.useContext(c),[O,C]=u.useState(null),P=null!==(f=null==O?void 0:O.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,L]=u.useState({}),T=(0,s.s)(t,e=>C(e)),D=Array.from(g.layers),[M]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),R=D.indexOf(M),x=O?D.indexOf(O):-1,S=g.layersWithOutsidePointerEventsDisabled.size>0,_=x>=R,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,l.c)(e),i=u.useRef(!1),o=u.useRef(()=>{});return u.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,u,{discrete:!0})},u={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));!_||n||(null==y||y(e),null==N||N(e),e.defaultPrevented||null==h||h())},P),A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,l.c)(e),i=u.useRef(!1);return u.useEffect(()=>{let e=e=>{e.target&&!i.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==b||b(e),null==N||N(e),e.defaultPrevented||null==h||h())},P);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);u.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{x===g.layers.size-1&&(null==E||E(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},P),u.useEffect(()=>{if(O)return v&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(O)),g.layers.add(O),m(),()=>{v&&1===g.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[O,P,v,g]),u.useEffect(()=>()=>{O&&(g.layers.delete(O),g.layersWithOutsidePointerEventsDisabled.delete(O),m())},[O,g]),u.useEffect(()=>{let e=()=>L({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,a.jsx)(o.sG.div,{...w,ref:T,style:{pointerEvents:S?_?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,A.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=u.forwardRef((e,t)=>{let n=u.useContext(c),r=u.useRef(null),i=(0,s.s)(t,r);return u.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(o.sG.div,{...e,ref:i})});function m(){let e=new CustomEvent(d);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:u}=r,i=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),u?(0,o.hO)(i,s):i.dispatchEvent(s)}v.displayName="DismissableLayerBranch";var E=f,y=v}}]);