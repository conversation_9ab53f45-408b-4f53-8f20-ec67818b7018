
"use client";

import React, { useState, useMemo } from "react";
import { useArchiveStore } from "@/store/archiveStore";
import type { ArchiveEntry } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Edit3, Trash2, Search, ChevronsLeft, ChevronLeft, ChevronRight, ChevronsRight } from "lucide-react";
import { PageHeader } from "@/components/shared/PageHeader";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const ITEMS_PER_PAGE = 10;

export default function ArchivePage() {
  const { entries: rawEntries, deleteEntry, getEntryById } = useArchiveStore();
  const entries = useMemo(() => rawEntries.map(entry => {
    const dateValue = entry.documentDate;
    const parsedDate = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return {
      ...entry,
      documentDate: parsedDate
    };
  }).sort((a, b) => {
    // Handle potentially invalid dates in sorting
    const timeA = a.documentDate instanceof Date && !isNaN(a.documentDate.getTime()) ? a.documentDate.getTime() : 0;
    const timeB = b.documentDate instanceof Date && !isNaN(b.documentDate.getTime()) ? b.documentDate.getTime() : 0;
    return timeB - timeA;
  }), [rawEntries]);
  
  const { toast } = useToast();
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const filteredEntries = useMemo(() => {
    return entries.filter(
      (entry) =>
        entry.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.documentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.contentEmployeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (entry.marginNotes && entry.marginNotes.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [entries, searchTerm]);

  const paginatedEntries = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredEntries.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [filteredEntries, currentPage]);

  const totalPages = Math.ceil(filteredEntries.length / ITEMS_PER_PAGE);

  const handleDelete = (id: string) => {
    deleteEntry(id);
    toast({
      title: "تم الحذف",
      description: "تم حذف القيد بنجاح.",
      variant: "destructive",
    });
    if (paginatedEntries.length === 1 && currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  const handleEdit = (id: string) => {
    const entryToEdit = getEntryById(id);
    if (entryToEdit) {
        router.push(`/data-entry?editId=${id}`);
        toast({ title: "تعديل قيد", description: `جاري تحضير القيد رقم ${entryToEdit.documentNumber} للتعديل.` });
    }
  };

  const formatDateSafely = (date: Date | string | undefined) => {
    if (!date) return "تاريخ غير محدد";
    const dateObj = date instanceof Date ? date : new Date(date);
    if (dateObj instanceof Date && !isNaN(dateObj.getTime())) {
      return format(dateObj, "dd/MM/yyyy", { locale: arSA });
    }
    return "تاريخ غير صالح";
  };

  return (
    <div className="space-y-6">
      <PageHeader title="الأرشيف العام" description="ابحث، استعرض، وعدّل قيود الأرشيف.">
        <div className="relative ml-auto flex-1 md:grow-0">
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              type="search"
              placeholder="ابحث في الأرشيف..."
              className="w-full rounded-lg bg-background pr-10 md:w-[250px] lg:w-[350px] glowing-input h-11 text-base"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); 
              }}
            />
        </div>
      </PageHeader>

      <Card className="shadow-lg">
        <CardHeader>
            <CardTitle>قائمة القيود ({filteredEntries.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-350px)]">
            <Table>
              <TableHeader className="sticky top-0 bg-muted/90 backdrop-blur-sm z-10">
                <TableRow>
                  <TableHead className="w-[150px] text-right">رقم الكتاب</TableHead>
                  <TableHead className="w-[130px] text-right">التاريخ</TableHead>
                  <TableHead className="min-w-[250px] text-right">الموضوع</TableHead>
                  <TableHead className="min-w-[200px] text-right">الفحوى/الموظف</TableHead>
                  <TableHead className="w-[180px] text-right">الدائرة</TableHead>
                  <TableHead className="min-w-[200px] text-right">الهامش</TableHead>
                  <TableHead className="w-[220px] text-center">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-32 text-center text-muted-foreground text-lg">
                      {searchTerm ? "لا توجد نتائج مطابقة للبحث." : "الأرشيف فارغ. قم بإضافة قيود جديدة."}
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedEntries.map((entry) => (
                    <TableRow key={entry.id} className="hover:bg-primary/5">
                      <TableCell className="font-medium text-right">{entry.documentNumber}</TableCell>
                      <TableCell className="text-right">
                        {formatDateSafely(entry.documentDate)}
                      </TableCell>
                      <TableCell className="text-right">{entry.subject}</TableCell>
                      <TableCell className="text-right max-w-xs truncate">{entry.contentEmployeeName}</TableCell>
                      <TableCell className="text-right">{entry.department}</TableCell>
                      <TableCell className={`text-right max-w-xs truncate ${entry.marginNotes ? 'text-margin-notes font-semibold' : ''}`}>{entry.marginNotes || "-"}</TableCell>
                      <TableCell className="space-x-2 space-x-reverse text-center"> 
                        <Button variant="outline" size="sm" onClick={() => handleEdit(entry.id)} className="glowing-button">
                          <Edit3 className="h-4 w-4 ml-1" /> تعديل
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm" className="glowing-button">
                              <Trash2 className="h-4 w-4 ml-1" /> حذف
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>هل أنت متأكد تمامًا؟</AlertDialogTitle>
                              <AlertDialogDescription>
                                هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف القيد بشكل دائم.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>إلغاء</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(entry.id)}>
                                نعم، قم بالحذف
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
               {filteredEntries.length > 0 && (
                <TableCaption>إجمالي {filteredEntries.length} قيد. عرض صفحة {currentPage} من {totalPages}.</TableCaption>
              )}
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
      
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 space-x-reverse py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
            className="glowing-button"
          >
            <ChevronsRight className="h-4 w-4" /> 
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="glowing-button"
          >
            <ChevronRight className="h-4 w-4" /> 
          </Button>
          <span className="text-sm text-muted-foreground">
            صفحة {currentPage} من {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="glowing-button"
          >
            <ChevronLeft className="h-4 w-4" /> 
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
            className="glowing-button"
          >
            <ChevronsLeft className="h-4 w-4" /> 
          </Button>
        </div>
      )}
    </div>
  );
}
    

    