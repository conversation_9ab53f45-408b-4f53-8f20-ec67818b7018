
"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarInset,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { AppLogo } from "@/components/AppLogo";
import {
  Home,
  FilePlus,
  BarChart3,
  Archive,
  Filter,
  Settings as SettingsIcon,
  Menu,
  LogOut,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useArchiveStore } from "@/store/archiveStore";
import { useEffect } from "react";
import { OutroAnimation } from "@/components/shared/OutroAnimation";

const navItems = [
  { href: "/dashboard", label: "الواجهة الرئيسية", icon: Home },
  { href: "/data-entry", label: "إدخال البيانات", icon: FilePlus },
  { href: "/reports", label: "التقرير", icon: BarChart3 },
  { href: "/archive", label: "الأرشيف العام", icon: Archive },
  { href: "/filter", label: "التصفية", icon: Filter },
  { href: "/settings", label: "الإعدادات", icon: SettingsIcon },
];

export default function MainAppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { settings, appState, setAppState } = useArchiveStore();

  useEffect(() => {
    document.body.className = '';
    document.documentElement.className = settings.theme;
    document.body.classList.add(settings.theme);
  }, [settings.theme]);

  const showSidebar = pathname !== "/dashboard";

  if (appState === 'exiting') {
    return <OutroAnimation />;
  }

  return (
    <SidebarProvider defaultOpen>
      <div className="flex min-h-screen bg-background" dir="rtl">
        {showSidebar && (
          <Sidebar side="right" collapsible="icon" className="border-l">
            <SidebarHeader className="p-4 flex items-center justify-between">
              <Link href="/dashboard" className="flex items-center gap-2">
                <AppLogo className="text-primary h-10 w-10" />
                <h1 className="text-2xl font-headline font-semibold text-primary group-data-[collapsible=icon]:hidden">
                  سفينة الأرشيف
                </h1>
              </Link>
            </SidebarHeader>
            <SidebarContent className="p-2">
              <SidebarMenu>
                {navItems.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <Link href={item.href} legacyBehavior passHref>
                      <SidebarMenuButton
                        className={cn(
                          "w-full justify-start text-base py-3",
                          pathname === item.href
                            ? "bg-primary/20 text-primary hover:bg-primary/30"
                            : "hover:bg-muted"
                        )}
                        isActive={pathname === item.href}
                        tooltip={{children: item.label, side: "left", className: "font-body"}}
                      >
                        <item.icon className="h-5 w-5" />
                        <span className="font-medium">{item.label}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarContent>
            <SidebarFooter className="p-4 group-data-[collapsible=icon]:p-2 mt-auto">
              <Button 
                variant="destructive" 
                className="w-full glowing-button"
                onClick={() => setAppState('exiting')}
              >
                <LogOut className="ml-2 h-5 w-5 group-data-[collapsible=icon]:ml-0" />
                <span className="group-data-[collapsible=icon]:hidden">الخروج من البرنامج</span>
              </Button>
            </SidebarFooter>
          </Sidebar>
        )}

        <SidebarInset className={cn("flex-1 flex flex-col overflow-auto", !showSidebar && "md:ml-0")}>
            <header className={cn(
                "sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/90 backdrop-blur-md px-6 shadow-md",
                !showSidebar && "justify-end" 
              )}
            >
               {showSidebar && (
                 <SidebarTrigger className="md:hidden">
                    <Menu className="h-6 w-6" />
                    <span className="sr-only">Toggle Menu</span>
                 </SidebarTrigger>
               )}
                <div className="flex-1">
                    {/* Dynamic page title could be set here via a context or store */}
                </div>
                 {!showSidebar && ( // Show AppLogo and Title if no sidebar (dashboard view)
                    <Link href="/dashboard" className="flex items-center gap-2">
                        <AppLogo className="text-primary h-8 w-8" />
                        <h1 className="text-xl font-headline font-semibold text-primary">
                            سفينة الأرشيف
                        </h1>
                    </Link>
                )}
            </header>
            <main className={cn("flex-1 bg-background/70", showSidebar ? "p-6 lg:p-8" : "p-0")}>
                 {children}
            </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
