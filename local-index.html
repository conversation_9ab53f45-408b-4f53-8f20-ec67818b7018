<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج الأرشفة الإلكترونية - سفينة الأرشيف</title>
    <meta name="description" content="برنامج الأرشفة الإلكترونية لإدارة المستندات بكفاءة">
    <link rel="icon" href="./favicon.ico" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            direction: rtl;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        
        h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            display: none;
        }
        
        .status.show {
            display: block;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        .footer {
            margin-top: 2rem;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        .loading {
            display: none;
            margin: 1rem 0;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📁</div>
        <h1>سفينة الأرشيف</h1>
        <p class="subtitle">برنامج الأرشفة الإلكترونية لإدارة المستندات بكفاءة</p>
        
        <div class="buttons">
            <button class="btn btn-primary" onclick="runApp()">
                🚀 تشغيل التطبيق
            </button>
            <button class="btn btn-secondary" onclick="openStatic()">
                📂 فتح الملفات الثابتة
            </button>
            <button class="btn btn-warning" onclick="showInstructions()">
                📖 تعليمات التشغيل
            </button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
        
        <div class="status" id="status"></div>
        
        <div class="footer">
            <p><strong>إعداد:</strong> المحاسب المبرمج علي عاجل خشان المحنة</p>
            <p><strong>الإصدار:</strong> 2.0.0</p>
            <p>&copy; 2025 جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status show ${type}`;
            
            setTimeout(() => {
                status.classList.remove('show');
            }, 5000);
        }
        
        function showLoading(show = true) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }
        
        function runApp() {
            showLoading(true);
            showStatus('جاري تشغيل التطبيق...', 'success');
            
            // Try to open the server version first
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        window.open('http://localhost:3000', '_blank');
                        showStatus('تم فتح التطبيق في نافذة جديدة', 'success');
                    } else {
                        throw new Error('Server not running');
                    }
                })
                .catch(() => {
                    // If server is not running, try to open static files
                    openStatic();
                })
                .finally(() => {
                    showLoading(false);
                });
        }
        
        function openStatic() {
            // Try to open the dashboard.html file
            const staticPath = './static/dashboard.html';
            
            // Check if static files exist
            fetch(staticPath)
                .then(response => {
                    if (response.ok) {
                        window.open(staticPath, '_blank');
                        showStatus('تم فتح الملفات الثابتة', 'success');
                    } else {
                        throw new Error('Static files not found');
                    }
                })
                .catch(() => {
                    showStatus('الملفات الثابتة غير موجودة. يرجى تشغيل: npm run build:static', 'error');
                });
        }
        
        function showInstructions() {
            const instructions = `
تعليمات تشغيل التطبيق:

1. للتشغيل العادي:
   - انقر على "تشغيل التطبيق"
   - أو استخدم: npm run run:app

2. للتشغيل على Windows:
   - انقر مرتين على run-app.bat
   - أو استخدم PowerShell: .\\run-app.ps1

3. للبناء فقط:
   - npm run build:static

4. للتشغيل بخادم محلي:
   - npm run start:static

5. لفتح الملفات مباشرة:
   - انقر على "فتح الملفات الثابتة"

متطلبات التشغيل:
- Node.js (الإصدار 18 أو أحدث)
- npm
- متصفح ويب حديث

للمساعدة:
- PowerShell: .\\run-app.ps1 -Help
            `;
            
            alert(instructions);
        }
        
        // Check if we're running locally
        if (window.location.protocol === 'file:') {
            showStatus('يتم تشغيل التطبيق محلياً. للحصول على أفضل تجربة، استخدم الخادم المحلي.', 'warning');
        }
    </script>
</body>
</html>
