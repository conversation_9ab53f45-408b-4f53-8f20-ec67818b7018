const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Function to open file in browser
function openInBrowser(filePath, browser = null) {
  const platform = process.platform;
  let command;
  
  if (platform === 'win32') {
    if (browser) {
      // Specific browser
      const browsers = {
        'chrome': 'chrome',
        'firefox': 'firefox',
        'edge': 'msedge',
        'opera': 'opera',
        'brave': 'brave'
      };
      
      if (browsers[browser.toLowerCase()]) {
        command = `start ${browsers[browser.toLowerCase()]} "${filePath}"`;
      } else {
        command = `start "${filePath}"`;
      }
    } else {
      // Default browser
      command = `start "${filePath}"`;
    }
  } else if (platform === 'darwin') {
    if (browser) {
      command = `open -a "${browser}" "${filePath}"`;
    } else {
      command = `open "${filePath}"`;
    }
  } else {
    if (browser) {
      command = `${browser} "${filePath}"`;
    } else {
      command = `xdg-open "${filePath}"`;
    }
  }
  
  exec(command, (error) => {
    if (error) {
      console.error(`خطأ في فتح المتصفح: ${error.message}`);
      console.log(`يرجى فتح الملف يدوياً: ${filePath}`);
    } else {
      console.log(`✅ تم فتح التطبيق في المتصفح: ${filePath}`);
    }
  });
}

// Get command line arguments
const args = process.argv.slice(2);
const browser = args[0]; // Optional browser name

// Check if static directory exists
const staticDir = path.join(__dirname, '..', 'static');
const indexPath = path.join(staticDir, 'index.html');

if (!fs.existsSync(indexPath)) {
  console.error('❌ ملف index.html غير موجود في مجلد static');
  console.log('يرجى تشغيل الأمر: npm run build:static');
  process.exit(1);
}

console.log('🚀 فتح التطبيق في المتصفح...');
openInBrowser(indexPath, browser);
