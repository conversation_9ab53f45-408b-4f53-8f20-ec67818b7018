import type { ReactNode } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react'; 

interface PageHeaderProps {
  title: string;
  description?: string;
  showBackButton?: boolean;
  backButtonHref?: string;
  backButtonText?: string;
  children?: ReactNode; 
}

export function PageHeader({
  title,
  description,
  showBackButton = false,
  backButtonHref = '/dashboard',
  backButtonText = 'العودة إلى الرئيسية',
  children,
}: PageHeaderProps) {
  return (
    <div className="mb-8 pb-6 border-b border-border/50">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="page-header-title-bg">
          <h1 className="text-4xl font-headline font-extrabold tracking-tight page-header-title-text">{title}</h1>
        </div>
        <div className="flex items-center gap-3 self-start md:self-center">
          {children}
          {showBackButton && (
            <Link href={backButtonHref} passHref>
              <Button variant="outline" size="lg" className="glowing-button">
                <ArrowRight className="ml-2 h-5 w-5" />
                {backButtonText}
              </Button>
            </Link>
          )}
        </div>
      </div>
      {description && <p className="text-muted-foreground mt-3 text-lg">{description}</p>}
    </div>
  );
}

    