
"use client";

import React from 'react';
import { AppLogo } from '@/components/AppLogo';

export function OutroAnimation() {
  return (
    <div className="fixed inset-0 z-[1000] flex flex-col items-center justify-center p-8 text-center outro-bg-animation text-white overflow-hidden">
      <div className="animate-fadeInScaleUpSlow space-y-8">
        <AppLogo className="w-28 h-28 md:w-36 md:h-36 mx-auto outro-logo-filter" />
        <h1 className="text-4xl md:text-6xl font-headline font-bold outro-text-main">
          نشكر استخدامكم لسفينة الأرشيف!
        </h1>
        <p className="text-xl md:text-2xl font-body outro-text-secondary">
          نتمنى لكم يوماً سعيداً وإبحاراً موفقاً في مهامكم.
        </p>
        <p className="text-lg md:text-xl font-body outro-text-subtle">
          مع تحيات فريق التطوير.
        </p>
      </div>
    </div>
  );
}
