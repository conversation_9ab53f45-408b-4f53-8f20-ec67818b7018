(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>l,r:()=>o});var r=t(5155),s=t(2115),i=t(9708),n=t(2085),d=t(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,a)=>{let{className:t,variant:s,size:n,asChild:l=!1,...c}=e,u=l?i.DX:"button";return(0,r.jsx)(u,{className:(0,d.cn)(o({variant:s,size:n,className:t})),ref:a,...c})});l.displayName="Button"},2011:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>ee});var r=t(5155),s=t(2115),i=t(6874),n=t.n(i),d=t(5695),o=t(9708),l=t(2085),c=t(5546),u=t(9434),f=t(285),b=t(2523),m=t(7489);let p=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...n}=e;return(0,r.jsx)(m.b,{ref:a,decorative:i,orientation:s,className:(0,u.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...n})});p.displayName=m.b.displayName;var h=t(5452),g=t(5318);let x=h.bL;h.l9,h.bm;let v=h.ZL,w=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(h.hJ,{className:(0,u.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s,ref:a})});w.displayName=h.hJ.displayName;let y=(0,l.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),N=s.forwardRef((e,a)=>{let{side:t="right",className:s,children:i,...n}=e;return(0,r.jsxs)(v,{children:[(0,r.jsx)(w,{}),(0,r.jsxs)(h.UC,{ref:a,className:(0,u.cn)(y({side:t}),s),...n,children:[i,(0,r.jsxs)(h.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});function j(e){let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,u.cn)("animate-pulse rounded-md bg-muted",a),...t})}N.displayName=h.UC.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(h.hE,{ref:a,className:(0,u.cn)("text-lg font-semibold text-foreground",t),...s})}).displayName=h.hE.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(h.VY,{ref:a,className:(0,u.cn)("text-sm text-muted-foreground",t),...s})}).displayName=h.VY.displayName;var k=t(9613);let S=k.Kq,z=k.bL,R=k.l9,_=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...i}=e;return(0,r.jsx)(k.UC,{ref:a,sideOffset:s,className:(0,u.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})});_.displayName=k.UC.displayName;let C=s.createContext(null);function M(){let e=s.useContext(C);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let E=s.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:i,onOpenChange:n,className:d,style:o,children:l,...c}=e,f=function(){let[e,a]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[b,m]=s.useState(!1),[p,h]=s.useState(t),g=null!=i?i:p,x=s.useCallback(e=>{let a="function"==typeof e?e(g):e;n?n(a):h(a),document.cookie="".concat("sidebar_state","=").concat(a,"; path=/; max-age=").concat(604800)},[n,g]),v=s.useCallback(()=>f?m(e=>!e):x(e=>!e),[f,x,m]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let w=g?"expanded":"collapsed",y=s.useMemo(()=>({state:w,open:g,setOpen:x,isMobile:f,openMobile:b,setOpenMobile:m,toggleSidebar:v}),[w,g,x,f,b,m,v]);return(0,r.jsx)(C.Provider,{value:y,children:(0,r.jsx)(S,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,u.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",d),ref:a,...c,children:l})})})});E.displayName="SidebarProvider";let A=s.forwardRef((e,a)=>{let{side:t="left",variant:s="sidebar",collapsible:i="offcanvas",className:n,children:d,...o}=e,{isMobile:l,state:c,openMobile:f,setOpenMobile:b}=M();return"none"===i?(0,r.jsx)("div",{className:(0,u.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:a,...o,children:d}):l?(0,r.jsx)(x,{open:f,onOpenChange:b,...o,children:(0,r.jsx)(N,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:d})})}):(0,r.jsxs)("div",{ref:a,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?i:"","data-variant":s,"data-side":t,children:[(0,r.jsx)("div",{className:(0,u.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===s||"inset"===s?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,u.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===s||"inset"===s?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:d})})]})});A.displayName="Sidebar";let D=s.forwardRef((e,a)=>{let{className:t,onClick:s,...i}=e,{toggleSidebar:n}=M();return(0,r.jsxs)(f.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,u.cn)("h-7 w-7",t),onClick:e=>{null==s||s(e),n()},...i,children:[(0,r.jsx)(c.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});D.displayName="SidebarTrigger",s.forwardRef((e,a)=>{let{className:t,...s}=e,{toggleSidebar:i}=M();return(0,r.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:i,title:"Toggle Sidebar",className:(0,u.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...s})}).displayName="SidebarRail";let L=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("main",{ref:a,className:(0,u.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...s})});L.displayName="SidebarInset",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(b.p,{ref:a,"data-sidebar":"input",className:(0,u.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...s})}).displayName="SidebarInput";let I=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,u.cn)("flex flex-col gap-2 p-2",t),...s})});I.displayName="SidebarHeader";let U=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,u.cn)("flex flex-col gap-2 p-2",t),...s})});U.displayName="SidebarFooter",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(p,{ref:a,"data-sidebar":"separator",className:(0,u.cn)("mx-2 w-auto bg-sidebar-border",t),...s})}).displayName="SidebarSeparator";let V=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,u.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...s})});V.displayName="SidebarContent",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,u.cn)("relative flex w-full min-w-0 flex-col p-2",t),...s})}).displayName="SidebarGroup",s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,...i}=e,n=s?o.DX:"div";return(0,r.jsx)(n,{ref:a,"data-sidebar":"group-label",className:(0,u.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...i})}).displayName="SidebarGroupLabel",s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,...i}=e,n=s?o.DX:"button";return(0,r.jsx)(n,{ref:a,"data-sidebar":"group-action",className:(0,u.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarGroupAction",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,u.cn)("w-full text-sm",t),...s})}).displayName="SidebarGroupContent";let B=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,u.cn)("flex w-full min-w-0 flex-col gap-1",t),...s})});B.displayName="SidebarMenu";let O=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,u.cn)("group/menu-item relative",t),...s})});O.displayName="SidebarMenuItem";let P=(0,l.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),T=s.forwardRef((e,a)=>{let{asChild:t=!1,isActive:s=!1,variant:i="default",size:n="default",tooltip:d,className:l,...c}=e,f=t?o.DX:"button",{isMobile:b,state:m}=M(),p=(0,r.jsx)(f,{ref:a,"data-sidebar":"menu-button","data-size":n,"data-active":s,className:(0,u.cn)(P({variant:i,size:n}),l),...c});return d?("string"==typeof d&&(d={children:d}),(0,r.jsxs)(z,{children:[(0,r.jsx)(R,{asChild:!0,children:p}),(0,r.jsx)(_,{side:"right",align:"center",hidden:"collapsed"!==m||b,...d})]})):p});T.displayName="SidebarMenuButton",s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,showOnHover:i=!1,...n}=e,d=s?o.DX:"button";return(0,r.jsx)(d,{ref:a,"data-sidebar":"menu-action",className:(0,u.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",i&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...n})}).displayName="SidebarMenuAction",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,u.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...s})}).displayName="SidebarMenuBadge",s.forwardRef((e,a)=>{let{className:t,showIcon:i=!1,...n}=e,d=s.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,r.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,u.cn)("rounded-md h-8 flex gap-2 px-2 items-center",t),...n,children:[i&&(0,r.jsx)(j,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(j,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}).displayName="SidebarMenuSkeleton",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,u.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...s})}).displayName="SidebarMenuSub",s.forwardRef((e,a)=>{let{...t}=e;return(0,r.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",s.forwardRef((e,a)=>{let{asChild:t=!1,size:s="md",isActive:i,className:n,...d}=e,l=t?o.DX:"a";return(0,r.jsx)(l,{ref:a,"data-sidebar":"menu-sub-button","data-size":s,"data-active":i,className:(0,u.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===s&&"text-xs","md"===s&&"text-sm","group-data-[collapsible=icon]:hidden",n),...d})}).displayName="SidebarMenuSubButton";var X=t(7404),F=t(8186),G=t(8603),K=t(5263),$=t(5064),q=t(9572),H=t(8271),W=t(3349),J=t(8341),Y=t(9404);function Z(){return(0,r.jsx)("div",{className:"fixed inset-0 z-[1000] flex flex-col items-center justify-center p-8 text-center outro-bg-animation text-white overflow-hidden",children:(0,r.jsxs)("div",{className:"animate-fadeInScaleUpSlow space-y-8",children:[(0,r.jsx)(X.v,{className:"w-28 h-28 md:w-36 md:h-36 mx-auto outro-logo-filter"}),(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-headline font-bold outro-text-main",children:"نشكر استخدامكم لسفينة الأرشيف!"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl font-body outro-text-secondary",children:"نتمنى لكم يوماً سعيداً وإبحاراً موفقاً في مهامكم."}),(0,r.jsx)("p",{className:"text-lg md:text-xl font-body outro-text-subtle",children:"مع تحيات فريق التطوير."})]})})}let Q=[{href:"/dashboard",label:"الواجهة الرئيسية",icon:F.A},{href:"/data-entry",label:"إدخال البيانات",icon:G.A},{href:"/reports",label:"التقرير",icon:K.A},{href:"/archive",label:"الأرشيف العام",icon:$.A},{href:"/filter",label:"التصفية",icon:q.A},{href:"/settings",label:"الإعدادات",icon:H.A}];function ee(e){let{children:a}=e,t=(0,d.usePathname)(),{settings:i,appState:o,setAppState:l}=(0,Y.f)();(0,s.useEffect)(()=>{document.body.className="",document.documentElement.className=i.theme,document.body.classList.add(i.theme)},[i.theme]);let c="/dashboard"!==t;return"exiting"===o?(0,r.jsx)(Z,{}):(0,r.jsx)(E,{defaultOpen:!0,children:(0,r.jsxs)("div",{className:"flex min-h-screen bg-background",dir:"rtl",children:[c&&(0,r.jsxs)(A,{side:"right",collapsible:"icon",className:"border-l",children:[(0,r.jsx)(I,{className:"p-4 flex items-center justify-between",children:(0,r.jsxs)(n(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(X.v,{className:"text-primary h-10 w-10"}),(0,r.jsx)("h1",{className:"text-2xl font-headline font-semibold text-primary group-data-[collapsible=icon]:hidden",children:"سفينة الأرشيف"})]})}),(0,r.jsx)(V,{className:"p-2",children:(0,r.jsx)(B,{children:Q.map(e=>(0,r.jsx)(O,{children:(0,r.jsx)(n(),{href:e.href,legacyBehavior:!0,passHref:!0,children:(0,r.jsxs)(T,{className:(0,u.cn)("w-full justify-start text-base py-3",t===e.href?"bg-primary/20 text-primary hover:bg-primary/30":"hover:bg-muted"),isActive:t===e.href,tooltip:{children:e.label,side:"left",className:"font-body"},children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"font-medium",children:e.label})]})})},e.href))})}),(0,r.jsx)(U,{className:"p-4 group-data-[collapsible=icon]:p-2 mt-auto",children:(0,r.jsxs)(f.$,{variant:"destructive",className:"w-full glowing-button",onClick:()=>l("exiting"),children:[(0,r.jsx)(W.A,{className:"ml-2 h-5 w-5 group-data-[collapsible=icon]:ml-0"}),(0,r.jsx)("span",{className:"group-data-[collapsible=icon]:hidden",children:"الخروج من البرنامج"})]})})]}),(0,r.jsxs)(L,{className:(0,u.cn)("flex-1 flex flex-col overflow-auto",!c&&"md:ml-0"),children:[(0,r.jsxs)("header",{className:(0,u.cn)("sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/90 backdrop-blur-md px-6 shadow-md",!c&&"justify-end"),children:[c&&(0,r.jsxs)(D,{className:"md:hidden",children:[(0,r.jsx)(J.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]}),(0,r.jsx)("div",{className:"flex-1"}),!c&&(0,r.jsxs)(n(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(X.v,{className:"text-primary h-8 w-8"}),(0,r.jsx)("h1",{className:"text-xl font-headline font-semibold text-primary",children:"سفينة الأرشيف"})]})]}),(0,r.jsx)("main",{className:(0,u.cn)("flex-1 bg-background/70",c?"p-6 lg:p-8":"p-0"),children:a})]})]})})}},2523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var r=t(5155),s=t(2115),i=t(9434);let n=s.forwardRef((e,a)=>{let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...n})});n.displayName="Input"},7404:(e,a,t)=>{"use strict";t.d(a,{v:()=>s});var r=t(5155);function s(e){return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round","aria-label":"Safina Archive Logo",...e,children:[(0,r.jsx)("path",{d:"M20 22H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2z"}),(0,r.jsx)("path",{d:"M9 3V21"})," ",(0,r.jsx)("path",{d:"M15 3V21"})," ",(0,r.jsx)("circle",{cx:"6.5",cy:"6.5",r:"0.5",fill:"currentColor"}),(0,r.jsx)("circle",{cx:"6.5",cy:"17.5",r:"0.5",fill:"currentColor"}),(0,r.jsx)("path",{d:"M4 10h16"})," "]})}},9404:(e,a,t)=>{"use strict";t.d(a,{f:()=>n});var r=t(8693),s=t(6786);let i={theme:"light"},n=(0,r.vt)()((0,s.Zr)((e,a)=>({entries:[],settings:i,appState:"running",addEntry:a=>{let t={...a,id:crypto.randomUUID()};e(e=>({entries:[...e.entries,t]}))},updateEntry:(a,t)=>e(e=>({entries:e.entries.map(e=>e.id===a?{...e,...t}:e)})),deleteEntry:a=>e(e=>({entries:e.entries.filter(e=>e.id!==a)})),setTheme:a=>e(e=>({settings:{...e.settings,theme:a}})),setAppState:a=>e(()=>({appState:a})),getEntryById:e=>a().entries.find(a=>a.id===e)}),{name:"safina-archive-storage",storage:(0,s.KU)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&(e.entries=e.entries.map(e=>({...e,documentDate:new Date(e.documentDate)})),e.appState="running")}}))},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var r=t(2596),s=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,r.$)(a))}},9436:(e,a,t)=>{Promise.resolve().then(t.bind(t,2011))}},e=>{var a=a=>e(e.s=a);e.O(0,[668,793,239,756,152,825,441,684,358],()=>a(9436)),_N_E=e.O()}]);