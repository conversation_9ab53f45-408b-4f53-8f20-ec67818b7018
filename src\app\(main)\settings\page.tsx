
"use client";

import React, { useEffect } from "react";
import { useArchiveStore } from "@/store/archiveStore";
import type { Theme } from "@/types";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PageHeader } from "@/components/shared/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Paintbrush, Info, Database, HardDrive } from "lucide-react";

const availableThemes: { value: Theme; label: string; description: string }[] = [
  { value: "light", label: "الوضع العادي (الفاتح)", description: "المظهر الافتراضي بألوان زاهية ومتوازنة." },
  { value: "dark", label: "الوضع الداكن", description: "مظهر مريح للعين في الإضاءة المنخفضة." },
  { value: "theme-ocean-sunset", label: "غروب المحيط", description: "ألوان المحيط الهادئة ممزوجة بدفء غروب الشمس." },
  { value: "theme-fiery-passion", label: "الشغف الناري", description: "ألوان حمراء وبرتقالية وصفراء قوية وحيوية." },
  { value: "theme-electric-pop", label: "البوب الكهربائي", description: "ألوان وردية وزرقاء وسماوية براقة وعصرية." },
];

export default function SettingsPage() {
  const { settings, setTheme } = useArchiveStore();

  useEffect(() => {
    document.body.className = ''; 
    document.documentElement.className = settings.theme; 
    document.body.classList.add(settings.theme); 
  }, [settings.theme]);

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme);
  };

  return (
    <div className="space-y-6">
      <PageHeader title="الإعدادات" description="قم بتخصيص مظهر وسلوك البرنامج." showBackButton />

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Paintbrush className="ml-2 h-6 w-6 text-primary" />
            تخصيص المظهر (الثيم)
          </CardTitle>
          <CardDescription>
            اختر الثيم المفضل لديك. سيتم تطبيق التغييرات فورًا على الواجهة.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={settings.theme}
            onValueChange={(value) => handleThemeChange(value as Theme)}
            className="gap-4"
            dir="rtl"
          >
            {availableThemes.map((themeOption) => (
              <Label
                key={themeOption.value}
                htmlFor={`theme-${themeOption.value}`}
                className="flex flex-col space-y-1 rounded-md border p-4 cursor-pointer hover:bg-accent/50 hover:text-accent-foreground [&:has([data-state=checked])]:border-primary [&:has([data-state=checked])]:bg-primary/10 [&:has([data-state=checked])]:ring-2 [&:has([data-state=checked])]:ring-primary"
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-lg">{themeOption.label}</span>
                  <RadioGroupItem value={themeOption.value} id={`theme-${themeOption.value}`} />
                </div>
                <span className="text-sm text-muted-foreground">{themeOption.description}</span>
              </Label>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="ml-2 h-6 w-6 text-primary" />
            إدارة البيانات وحفظها
          </CardTitle>
          <CardDescription>
            معلومات حول كيفية حفظ بيانات برنامج الأرشفة.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-1 flex items-center">
              <HardDrive className="ml-2 h-5 w-5 text-muted-foreground" />
              التخزين الأساسي للبيانات:
            </h3>
            <p className="text-muted-foreground">
              يتم حفظ جميع قيود الأرشيف والإعدادات بشكل دائم وآمن في التخزين المحلي للمتصفح الخاص بك (localStorage). هذا هو "قاعدة بيانات البرنامج" ويعمل طالما أنك تستخدم نفس المتصفح ولم تقم بمسح بيانات الموقع يدويًا.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-1 flex items-center">
              <Info className="ml-2 h-5 w-5 text-muted-foreground" />
              حفظ الملفات على الحاسوب (التنزيلات):
            </h3>
            <p className="text-muted-foreground">
              عندما تقوم بإضافة قيد جديد في صفحة "إدخال البيانات"، يتم تلقائيًا تنزيل نسخة JSON من هذا القيد إلى جهازك.
              كذلك، يمكنك تصدير البيانات كملفات Excel من صفحات "التقرير العام" و "التصفية".
            </p>
            <p className="text-muted-foreground mt-2">
              <strong>ملاحظة هامة حول مسار الحفظ:</strong> تطبيقات الويب (مثل هذا البرنامج) تعمل ضمن بيئة آمنة ومقيدة يوفرها متصفح الويب. لأسباب تتعلق بالأمان وحماية خصوصية المستخدم، <strong>لا يمكن لتطبيقات الويب الوصول المباشر إلى نظام الملفات على جهاز الكمبيوتر الخاص بك أو تحديد مسار حفظ افتراضي لملفات البرنامج الرئيسية.</strong>
            </p>
            <p className="text-muted-foreground mt-2">
              الملفات التي يتم تنزيلها (JSON، Excel) يتم التعامل معها بواسطة متصفح الويب الخاص بك. عادةً ما يتم حفظها في مجلد "التنزيلات" (Downloads) الافتراضي لديك، أو قد يسألك المتصفح عن المكان الذي تريد حفظ الملف فيه، وذلك بناءً على إعدادات متصفحك. لا يمكن للبرنامج التحكم في هذا المسار بشكل مباشر.
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
            <CardTitle className="flex items-center">
                <Info className="ml-2 h-6 w-6 text-primary" />
                دليل المستخدم والتعليمات
            </CardTitle>
            <CardDescription>للمساعدة في استخدام البرنامج، يرجى الرجوع إلى دليل المستخدم.</CardDescription>
        </CardHeader>
        <CardContent>
            <p className="text-muted-foreground">
                (سيتم إضافة دليل المستخدم هنا في الإصدارات القادمة، حاليًا يمكنك استكشاف البرنامج والاعتماد على الواجهة الواضحة.)
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" disabled>
                عرض دليل المستخدم (غير متوفر حاليًا)
            </Button>
        </CardContent>
      </Card>
    </div>
  );
}
