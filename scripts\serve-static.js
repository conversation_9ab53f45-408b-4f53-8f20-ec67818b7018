const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const PORT = 3000;
const staticDir = path.join(__dirname, '..', 'static');

if (!fs.existsSync(staticDir)) {
  console.error('Static directory not found. Please run "npm run build:static" first.');
  process.exit(1);
}

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

const server = http.createServer((req, res) => {
  let filePath = path.join(staticDir, req.url);

  // Handle root path
  if (req.url === '/') {
    filePath = path.join(staticDir, 'dashboard', 'index.html');
  }
  // Handle specific routes
  else if (req.url === '/dashboard') {
    filePath = path.join(staticDir, 'dashboard', 'index.html');
  }
  else if (req.url === '/data-entry') {
    filePath = path.join(staticDir, 'data-entry', 'index.html');
  }
  else if (req.url === '/archive') {
    filePath = path.join(staticDir, 'archive', 'index.html');
  }
  else if (req.url === '/reports') {
    filePath = path.join(staticDir, 'reports', 'index.html');
  }
  else if (req.url === '/filter') {
    filePath = path.join(staticDir, 'filter', 'index.html');
  }
  else if (req.url === '/settings') {
    filePath = path.join(staticDir, 'settings', 'index.html');
  }

  // Check if file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File not found, serve dashboard as fallback
      filePath = path.join(staticDir, 'dashboard', 'index.html');
    }

    // Get file extension and content type
    const extname = path.extname(filePath).toLowerCase();
    const contentType = mimeTypes[extname] || 'application/octet-stream';

    // Read and serve file
    fs.readFile(filePath, (error, content) => {
      if (error) {
        res.writeHead(500);
        res.end('Server Error: ' + error.code);
      } else {
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content, 'utf-8');
      }
    });
  });
});

server.listen(PORT, () => {
  console.log(`🚀 التطبيق يعمل على: http://localhost:${PORT}`);
  console.log('📱 يمكنك الآن فتح التطبيق في المتصفح');
  
  // Automatically open browser
  const url = `http://localhost:${PORT}`;
  
  // Detect platform and open browser
  const platform = process.platform;
  let command;
  
  if (platform === 'win32') {
    command = `start ${url}`;
  } else if (platform === 'darwin') {
    command = `open ${url}`;
  } else {
    command = `xdg-open ${url}`;
  }
  
  exec(command, (error) => {
    if (error) {
      console.log(`⚠️  لم يتم فتح المتصفح تلقائياً. يرجى فتح الرابط يدوياً: ${url}`);
    } else {
      console.log('✅ تم فتح المتصفح تلقائياً');
    }
  });
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  server.close(() => {
    console.log('✅ تم إيقاف الخادم بنجاح');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إيقاف الخادم...');
  server.close(() => {
    console.log('✅ تم إيقاف الخادم بنجاح');
    process.exit(0);
  });
});
