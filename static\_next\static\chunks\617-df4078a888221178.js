"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[617],{518:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},679:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},1992:(e,t,r)=>{r.d(t,{In:()=>eE,JU:()=>e_,LM:()=>eL,PP:()=>eO,UC:()=>eD,VF:()=>eq,WT:()=>eI,YJ:()=>eH,ZL:()=>eA,bL:()=>eT,l9:()=>eP,p4:()=>eV,q7:()=>eB,wn:()=>eF,wv:()=>eG});var l=r(2115),n=r(7650),o=r(9367),a=r(5185),i=r(2284),s=r(6101),d=r(6081),u=r(4315),c=r(9178),p=r(2293),f=r(7900),v=r(1285),h=r(5152),m=r(4378),w=r(3655),g=r(9708),y=r(1414),x=r(5845),S=r(2712),b=r(5503),C=r(2564),k=r(8168),j=r(3795),R=r(5155),N=[" ","Enter","ArrowUp","ArrowDown"],M=[" ","Enter"],T="Select",[P,I,E]=(0,i.N)(T),[A,D]=(0,d.A)(T,[E,h.Bk]),L=(0,h.Bk)(),[H,_]=A(T),[B,V]=A(T),q=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,y=L(t),[S,b]=l.useState(null),[C,k]=l.useState(null),[j,N]=l.useState(!1),M=(0,u.jH)(c),[T=!1,I]=(0,x.i)({prop:n,defaultProp:o,onChange:a}),[E,A]=(0,x.i)({prop:i,defaultProp:s,onChange:d}),D=l.useRef(null),_=!S||g||!!S.closest("form"),[V,q]=l.useState(new Set),O=Array.from(V).map(e=>e.props.value).join(";");return(0,R.jsx)(h.bL,{...y,children:(0,R.jsxs)(H,{required:w,scope:t,trigger:S,onTriggerChange:b,valueNode:C,onValueNodeChange:k,valueNodeHasChildren:j,onValueNodeHasChildrenChange:N,contentId:(0,v.B)(),value:E,onValueChange:A,open:T,onOpenChange:I,dir:M,triggerPointerDownPosRef:D,disabled:m,children:[(0,R.jsx)(P.Provider,{scope:t,children:(0,R.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{q(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{q(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,R.jsxs)(eR,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:E,onChange:e=>A(e.target.value),disabled:m,form:g,children:[void 0===E?(0,R.jsx)("option",{value:""}):null,Array.from(V)]},O):null]})})};q.displayName=T;var O="SelectTrigger",F=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,i=L(r),d=_(O,r),u=d.disabled||n,c=(0,s.s)(t,d.onTriggerChange),p=I(r),f=l.useRef("touch"),[v,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),l=eM(t,e,r);void 0!==l&&d.onValueChange(l.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,R.jsx)(h.Mz,{asChild:!0,...i,children:(0,R.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ej(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&N.includes(e.key)&&(y(),e.preventDefault())})})})});F.displayName=O;var G="SelectValue",K=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:n,children:o,placeholder:a="",...i}=e,d=_(G,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,S.N)(()=>{u(c)},[u,c]),(0,R.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(d.value)?(0,R.jsx)(R.Fragment,{children:a}):o})});K.displayName=G;var U=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:l,...n}=e;return(0,R.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t,children:l||"▼"})});U.displayName="SelectIcon";var W=e=>(0,R.jsx)(m.Z,{asChild:!0,...e});W.displayName="SelectPortal";var z="SelectContent",Z=l.forwardRef((e,t)=>{let r=_(z,e.__scopeSelect),[o,a]=l.useState();return((0,S.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,R.jsx)(J,{...e,ref:t}):o?n.createPortal((0,R.jsx)(X,{scope:e.__scopeSelect,children:(0,R.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,R.jsx)("div",{children:e.children})})}),o):null});Z.displayName=z;var[X,Y]=A(z),J=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:S,hideWhenDetached:b,avoidCollisions:C,...N}=e,M=_(z,r),[T,P]=l.useState(null),[E,A]=l.useState(null),D=(0,s.s)(t,e=>P(e)),[L,H]=l.useState(null),[B,V]=l.useState(null),q=I(r),[O,F]=l.useState(!1),G=l.useRef(!1);l.useEffect(()=>{if(T)return(0,k.Eq)(T)},[T]),(0,p.Oh)();let K=l.useCallback(e=>{let[t,...r]=q().map(e=>e.ref.current),[l]=r.slice(-1),n=document.activeElement;for(let r of e)if(r===n||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===l&&E&&(E.scrollTop=E.scrollHeight),null==r||r.focus(),document.activeElement!==n))return},[q,E]),U=l.useCallback(()=>K([L,T]),[K,L,T]);l.useEffect(()=>{O&&U()},[O,U]);let{onOpenChange:W,triggerPointerDownPosRef:Z}=M;l.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var r,l,n,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(n=null===(r=Z.current)||void 0===r?void 0:r.x)&&void 0!==n?n:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(l=Z.current)||void 0===l?void 0:l.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,W,Z]),l.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[Y,J]=eN(e=>{let t=q().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),l=eM(t,e,r);l&&setTimeout(()=>l.ref.current.focus())}),ee=l.useCallback((e,t,r)=>{let l=!G.current&&!r;(void 0!==M.value&&M.value===t||l)&&(H(e),l&&(G.current=!0))},[M.value]),et=l.useCallback(()=>null==T?void 0:T.focus(),[T]),er=l.useCallback((e,t,r)=>{let l=!G.current&&!r;(void 0!==M.value&&M.value===t||l)&&V(e)},[M.value]),el="popper"===n?$:Q,en=el===$?{side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:S,hideWhenDetached:b,avoidCollisions:C}:{};return(0,R.jsx)(X,{scope:r,content:T,viewport:E,onViewportChange:A,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:B,position:n,isPositioned:O,searchRef:Y,children:(0,R.jsx)(j.A,{as:g.DX,allowPinchZoom:!0,children:(0,R.jsx)(f.n,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null===(t=M.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,R.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,R.jsx)(el,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...N,...en,onPlaced:()=>F(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...N.style},onKeyDown:(0,a.m)(N.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=q().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,l=t.indexOf(r);t=t.slice(l+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,i=_(z,r),d=Y(z,r),[u,c]=l.useState(null),[p,f]=l.useState(null),v=(0,s.s)(t,e=>f(e)),h=I(r),m=l.useRef(!1),g=l.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:b,focusSelectedItem:C}=d,k=l.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&b){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),l=b.getBoundingClientRect();if("rtl"!==i.dir){let n=l.left-t.left,a=r.left-n,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.left=p+"px"}else{let n=t.right-l.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.right=p+"px"}let a=h(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+d+parseInt(c.paddingBottom,10)+w,S=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(y),k=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,N=x.offsetHeight/2,M=f+v+(x.offsetTop+N);if(M<=R){let e=a.length>0&&x===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-R,N+(e?j:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=M+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;u.style.top="0px";let t=Math.max(R,f+y.offsetTop+(e?k:0)+N);u.style.height=t+(g-M)+"px",y.scrollTop=M-R+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=S+"px",u.style.maxHeight=s+"px",null==n||n(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,y,x,b,i.dir,n]);(0,S.N)(()=>k(),[k]);let[j,N]=l.useState();(0,S.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let M=l.useCallback(e=>{e&&!0===g.current&&(k(),null==C||C(),g.current=!1)},[k,C]);return(0,R.jsx)(ee,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:M,children:(0,R.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,R.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:l="start",collisionPadding:n=10,...o}=e,a=L(r);return(0,R.jsx)(h.UC,{...a,...o,ref:t,align:l,collisionPadding:n,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=A(z,{}),er="SelectViewport",el=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,i=Y(er,r),d=et(er,r),u=(0,s.s)(t,i.onViewportChange),c=l.useRef(0);return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,R.jsx)(P.Slot,{scope:r,children:(0,R.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:l}=d;if((null==l?void 0:l.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let l=window.innerHeight-20,n=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(n<l){let o=n+e,a=Math.min(l,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=er;var en="SelectGroup",[eo,ea]=A(en),ei=l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=(0,v.B)();return(0,R.jsx)(eo,{scope:r,id:n,children:(0,R.jsx)(w.sG.div,{role:"group","aria-labelledby":n,...l,ref:t})})});ei.displayName=en;var es="SelectLabel",ed=l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=ea(es,r);return(0,R.jsx)(w.sG.div,{id:n.id,...l,ref:t})});ed.displayName=es;var eu="SelectItem",[ec,ep]=A(eu),ef=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:i,...d}=e,u=_(eu,r),c=Y(eu,r),p=u.value===n,[f,h]=l.useState(null!=i?i:""),[m,g]=l.useState(!1),y=(0,s.s)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,n,o)}),x=(0,v.B)(),S=l.useRef("touch"),b=()=>{o||(u.onValueChange(n),u.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,R.jsx)(ec,{scope:r,value:n,disabled:o,textId:x,isSelected:p,onItemTextChange:l.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,R.jsx)(P.ItemSlot,{scope:r,value:n,disabled:o,textValue:f,children:(0,R.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==S.current&&b()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===S.current&&b()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null===(t=c.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(M.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ev="SelectItemText",eh=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=_(ev,r),u=Y(ev,r),c=ep(ev,r),p=V(ev,r),[f,v]=l.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=u.itemTextRefCallback)||void 0===t?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=l.useMemo(()=>(0,R.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,S.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?n.createPortal(i.children,d.valueNode):null]})});eh.displayName=ev;var em="SelectItemIndicator",ew=l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e;return ep(em,r).isSelected?(0,R.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t}):null});ew.displayName=em;var eg="SelectScrollUpButton",ey=l.forwardRef((e,t)=>{let r=Y(eg,e.__scopeSelect),n=et(eg,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,R.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=eg;var ex="SelectScrollDownButton",eS=l.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),n=et(ex,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,R.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=ex;var eb=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,i=Y("SelectScrollButton",r),s=l.useRef(null),d=I(r),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),(0,S.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[d]),(0,R.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),eC=l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e;return(0,R.jsx)(w.sG.div,{"aria-hidden":!0,...l,ref:t})});eC.displayName="SelectSeparator";var ek="SelectArrow";function ej(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=L(r),o=_(ek,r),a=Y(ek,r);return o.open&&"popper"===a.position?(0,R.jsx)(h.i3,{...n,...l,ref:t}):null}).displayName=ek;var eR=l.forwardRef((e,t)=>{let{value:r,...n}=e,o=l.useRef(null),a=(0,s.s)(t,o),i=(0,b.Z)(r);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let l=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(l)}},[i,r]),(0,R.jsx)(C.s,{asChild:!0,children:(0,R.jsx)("select",{...n,ref:a,defaultValue:r})})});function eN(e){let t=(0,y.c)(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let l=r.current+e;t(l),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),a=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function eM(e,t,r){var l,n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(l=e,n=Math.max(a,0),l.map((e,t)=>l[(n+t)%l.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eR.displayName="BubbleSelect";var eT=q,eP=F,eI=K,eE=U,eA=W,eD=Z,eL=el,eH=ei,e_=ed,eB=ef,eV=eh,eq=ew,eO=ey,eF=eS,eG=eC},2284:(e,t,r)=>{r.d(t,{N:()=>s});var l=r(2115),n=r(6081),o=r(6101),a=r(9708),i=r(5155);function s(e){let t=e+"CollectionProvider",[r,s]=(0,n.A)(t),[d,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,n=l.useRef(null),o=l.useRef(new Map).current;return(0,i.jsx)(d,{scope:t,itemMap:o,collectionRef:n,children:r})};c.displayName=t;let p=e+"CollectionSlot",f=l.forwardRef((e,t)=>{let{scope:r,children:l}=e,n=u(p,r),s=(0,o.s)(t,n.collectionRef);return(0,i.jsx)(a.DX,{ref:s,children:l})});f.displayName=p;let v=e+"CollectionItemSlot",h="data-radix-collection-item",m=l.forwardRef((e,t)=>{let{scope:r,children:n,...s}=e,d=l.useRef(null),c=(0,o.s)(t,d),p=u(v,r);return l.useEffect(()=>(p.itemMap.set(d,{ref:d,...s}),()=>void p.itemMap.delete(d))),(0,i.jsx)(a.DX,{[h]:"",ref:c,children:n})});return m.displayName=v,[{Provider:c,Slot:f,ItemSlot:m},function(t){let r=u(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},2564:(e,t,r)=>{r.d(t,{b:()=>i,s:()=>a});var l=r(2115),n=r(3655),o=r(5155),a=l.forwardRef((e,t)=>(0,o.jsx)(n.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var i=a},5074:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},5503:(e,t,r)=>{r.d(t,{Z:()=>n});var l=r(2115);function n(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},7381:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8531:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},9556:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}}]);