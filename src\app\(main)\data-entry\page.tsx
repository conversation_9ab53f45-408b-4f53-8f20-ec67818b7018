
"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Save } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";
import { useArchiveStore } from "@/store/archiveStore";
import { useToast } from "@/hooks/use-toast";
import { PageHeader } from "@/components/shared/PageHeader";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import type { ArchiveEntry } from "@/types";

const documentEntrySchema = z.object({
  documentNumber: z.string().min(1, "رقم الكتاب مطلوب"),
  documentDate: z.date({ required_error: "تاريخ الكتاب مطلوب" }),
  subject: z.string().min(1, "الموضوع مطلوب"),
  contentEmployeeName: z.string().min(1, "الفحوى أو اسم الموظف مطلوب"),
  department: z.string().min(1, "الدائرة المعنية مطلوبة"),
  marginNotes: z.string().optional(),
});

type DocumentEntryFormValues = z.infer<typeof documentEntrySchema>;

export default function DataEntryPage() {
  const addEntry = useArchiveStore((state) => state.addEntry);
  const { toast } = useToast();

  const form = useForm<DocumentEntryFormValues>({
    resolver: zodResolver(documentEntrySchema),
    defaultValues: {
      documentNumber: "",
      subject: "",
      contentEmployeeName: "",
      department: "",
      marginNotes: "",
      documentDate: undefined,
    },
  });

  function onSubmit(data: DocumentEntryFormValues) {
    // Correctly prepare the data for addEntry
    const newEntryData: Omit<ArchiveEntry, 'id'> = {
      ...data,
      marginNotes: data.marginNotes || "", // Ensure marginNotes is always a string
    };
    addEntry(newEntryData); // Pass the corrected object

    toast({
      title: "تم الحفظ",
      description: "تم حفظ القيد الجديد في أرشيف البرنامج.",
      variant: "default",
    });
    form.reset();
  }

  return (
    <div className="space-y-6">
      <PageHeader title="إدخال البيانات" description="قم بإدخال تفاصيل المستند الجديد هنا." />
      <Card className="max-w-5xl mx-auto shadow-lg">
        <CardHeader>
          <CardTitle>نموذج قيد جديد</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <FormField
                  control={form.control}
                  name="documentNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-lg">رقم الكتاب</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="مثال: 123/أ/2023"
                          {...field}
                          className="glowing-input text-base py-2.5"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="documentDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="text-lg">تاريخ الكتاب</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal text-base py-2.5 h-auto glowing-input",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: arSA })
                              ) : (
                                <span>اختر تاريخًا</span>
                              )}
                              <CalendarIcon className="mr-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="end">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                            dir="rtl"
                            locale={arSA}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg">الموضوع</FormLabel>
                    <FormControl>
                      <Input placeholder="موضوع الكتاب" {...field} className="glowing-input text-base py-2.5" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contentEmployeeName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg">الفحوى أو اسم الموظف المعني</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="أدخل فحوى الكتاب أو اسم الموظف المسؤول..."
                        className="resize-y min-h-[120px] glowing-input text-base"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      يمكنك إدخال نص طويل هنا.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg">الدائرة المعنية</FormLabel>
                    <FormControl>
                      <Input placeholder="اسم الدائرة أو القسم" {...field} className="glowing-input text-base py-2.5" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marginNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg">الهامش (ملاحظات إضافية)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="أضف أي ملاحظات أو تعليقات هامشية هنا..."
                        className="resize-y min-h-[100px] glowing-input text-margin-notes text-base"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center gap-4">
                <Button type="submit" size="lg" className="glowing-button text-lg py-3 px-8">
                  <Save className="ml-2 h-5 w-5" />
                  الحفظ
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
