
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import type { ArchiveEntry } from '@/types';

const formatDateForExport = (dateValue: Date | string | undefined): string => {
  if (!dateValue) return "تاريخ غير محدد";
  const dateObj = dateValue instanceof Date ? dateValue : new Date(dateValue);
  if (dateObj instanceof Date && !isNaN(dateObj.getTime())) {
    return format(dateObj, "dd MMMM yyyy", { locale: arSA });
  }
  return "تاريخ غير صالح";
};

export function exportToExcel(data: ArchiveEntry[], fileName: string): void {
  if (!data || data.length === 0) {
    console.warn("No data to export.");
    return;
  }

  const headers = [
    "الهامش",
    "الدائرة",
    "الفحوى/الموظف",
    "الموضوع",
    "تاريخ الكتاب",
    "رقم الكتاب",
  ];

  const mappedData = data.map(entry => ([
    entry.marginNotes || "-",
    entry.department,
    entry.contentEmployeeName,
    entry.subject,
    formatDateForExport(entry.documentDate),
    entry.documentNumber,
  ]));

  const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([headers, ...mappedData]);

  const columnWidths = headers.map((_, i) => {
    let maxWidth = 0;
    [headers, ...mappedData].forEach(row => {
      const cellValue = row[i] ? String(row[i]) : '';
      if (cellValue.length > maxWidth) {
        maxWidth = cellValue.length;
      }
    });
    return { wch: Math.min(Math.max(maxWidth, 10), 50) }; 
  });
  ws['!cols'] = columnWidths;

  const wb: XLSX.WorkBook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "البيانات");

  XLSX.writeFile(wb, fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`);
}

export function exportFilteredDataToExcel(data: ArchiveEntry[], fileName: string): void {
  if (!data || data.length === 0) {
    console.warn("No data to export.");
    return;
  }

  const headers = [
    "رقم الكتاب",
    "تاريخ الكتاب",
    "الموضوع",
    "الفحوى/الموظف",
    "الدائرة",
    "الهامش",
  ];

  const mappedData = data.map(entry => ([
    entry.documentNumber,
    formatDateForExport(entry.documentDate),
    entry.subject,
    entry.contentEmployeeName,
    entry.department,
    entry.marginNotes || "-",
  ]));

  const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([headers, ...mappedData]);
  
  const columnWidths = headers.map((_, i) => {
    let maxWidth = 0;
    [headers, ...mappedData].forEach(row => {
      const cellValue = row[i] ? String(row[i]) : '';
      if (cellValue.length > maxWidth) {
        maxWidth = cellValue.length;
      }
    });
    return { wch: Math.min(Math.max(maxWidth, 10), 50) };
  });
  ws['!cols'] = columnWidths;

  const wb: XLSX.WorkBook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "نتائج التصفية");
  XLSX.writeFile(wb, fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`);
}

    