
"use client";

import React, { useEffect, useState } from 'react';
import { AppLogo } from '@/components/AppLogo';
import { Button } from '@/components/ui/button';

interface IntroAnimationProps {
  onAnimationComplete: () => void;
}

export function IntroAnimation({ onAnimationComplete }: IntroAnimationProps) {
  const [step, setStep] = useState(0);
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    const timeouts: NodeJS.Timeout[] = [];
    if (step === 0) { // Logo and Program Name
      timeouts.push(setTimeout(() => setStep(1), 3500)); // Show logo/name for 3.5s
    }
    if (step === 1) { // Programmer Name
      timeouts.push(setTimeout(() => setStep(2), 3500)); // Show programmer name for 3.5s
    }
    if (step === 2) { // Copyright & Version, then fade out
      timeouts.push(setTimeout(() => {
        setFadeOut(true);
        timeouts.push(setTimeout(onAnimationComplete, 800)); // Wait for fade out animation
      }, 3000)); // Show copyright/version for 3s
    }
    return () => timeouts.forEach(clearTimeout);
  }, [step, onAnimationComplete]);

  const handleSkip = () => {
    setFadeOut(true);
    setTimeout(onAnimationComplete, 800); // Wait for fade out animation
  };

  return (
    <div 
      className={`fixed inset-0 z-[1000] flex flex-col items-center justify-center p-8 text-center intro-bg-animation overflow-hidden transition-opacity duration-700 ease-in-out ${fadeOut ? 'opacity-0' : 'opacity-100'}`}
    >
      {step === 0 && (
        <div className="animate-fadeInScaleUpSlightly space-y-6 md:space-y-8">
          <AppLogo className="w-32 h-32 md:w-40 md:h-40 mx-auto intro-logo-filter animate-pulseSubtle" />
          <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-headline font-bold intro-text-program">
            سفينة الأرشيف
          </h1>
          <p className="text-xl sm:text-2xl md:text-3xl font-body intro-subtitle-program">
            برنامج الأرشفة الإلكترونية
          </p>
        </div>
      )}
      {step === 1 && (
        <div className="animate-slideInFromBottomSmooth space-y-4 md:space-y-5">
          <p className="text-lg sm:text-xl md:text-2xl font-body intro-text-secondary">إعداد وتقديم:</p>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-headline font-extrabold intro-text-programmer">
            المحاسب المبرمج علي عاجل خشان المحنة
          </h2>
        </div>
      )}
      {step === 2 && (
        <div className="animate-fadeInDelayed space-y-3">
          <p className="text-base sm:text-lg md:text-xl font-body intro-text-copyright">
            &copy; {new Date().getFullYear()} جميع الحقوق محفوظة.
          </p>
          <p className="text-sm sm:text-md md:text-lg font-body intro-text-version">
            الإصدار: 2.0.0
          </p>
        </div>
      )}
      <Button 
        onClick={handleSkip} 
        variant="ghost" 
        className="absolute bottom-6 right-6 md:bottom-8 md:right-8 text-white/60 hover:text-white hover:bg-white/10 text-xs py-1 px-2 rounded-full transition-opacity duration-300 animate-fadeInDelayed"
        style={{ animationDelay: '1s' }}
      >
        تخطي المقدمة
      </Button>
    </div>
  );
}
