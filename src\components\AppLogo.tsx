import type { SVGProps } from 'react';

export function AppLogo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      aria-label="Safina Archive Logo"
      {...props}
    >
      <path d="M20 22H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2z" />
      <path d="M9 3V21" /> {/* Represents spine of a book/archive folder */}
      <path d="M15 3V21" /> {/* Another line for folder structure */}
      <circle cx="6.5" cy="6.5" r="0.5" fill="currentColor" />
      <circle cx="6.5" cy="17.5" r="0.5" fill="currentColor" />
      <path d="M4 10h16" /> {/* Represents a shelf or separator */}
    </svg>
  );
}
