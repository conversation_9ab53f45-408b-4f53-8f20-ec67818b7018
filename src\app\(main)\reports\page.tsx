
"use client";

import { useArchiveStore } from "@/store/archiveStore";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/shared/PageHeader";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useMemo } from "react";
import { FileSpreadsheet } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel } from "@/lib/exportUtils"; 

export default function ReportsPage() {
  const { toast } = useToast();
  const entries = useArchiveStore((state) => 
    useMemo(() => 
      state.entries.map(entry => {
        const dateValue = entry.documentDate;
        const parsedDate = dateValue instanceof Date ? dateValue : new Date(dateValue);
        return {
          ...entry,
          documentDate: parsedDate
        };
      }).sort((a, b) => {
        const timeA = a.documentDate instanceof Date && !isNaN(a.documentDate.getTime()) ? a.documentDate.getTime() : 0;
        const timeB = b.documentDate instanceof Date && !isNaN(b.documentDate.getTime()) ? b.documentDate.getTime() : 0;
        return timeB - timeA;
      }),
      [state.entries]
    )
  );

  const handleExport = () => {
    if (entries.length === 0) {
      toast({
        title: "لا توجد بيانات للتصدير",
        description: "يرجى إضافة قيود أولاً.",
        variant: "destructive",
      });
      return;
    }
    try {
      exportToExcel(entries, "التقرير_العام.xlsx");
      toast({
        title: "تم التصدير بنجاح",
        description: "تم تنزيل ملف Excel بالبيانات.",
        variant: "default",
      });
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({
        title: "خطأ في التصدير",
        description: "حدث خطأ أثناء محاولة تصدير البيانات.",
        variant: "destructive",
      });
    }
  };

  const formatDateSafely = (date: Date | string | undefined) => {
    if (!date) return "تاريخ غير محدد";
    const dateObj = date instanceof Date ? date : new Date(date);
    if (dateObj instanceof Date && !isNaN(dateObj.getTime())) {
      return format(dateObj, "dd MMMM yyyy", { locale: arSA });
    }
    return "تاريخ غير صالح";
  };

  return (
    <div className="space-y-6">
      <PageHeader title="التقرير العام" description="عرض جميع البيانات المدخلة في الأرشيف." showBackButton>
        <Button variant="outline" size="lg" className="glowing-button" onClick={handleExport}>
          <FileSpreadsheet className="ml-2 h-5 w-5" />
          تصدير إلى Excel (.xlsx)
        </Button>
      </PageHeader>
       <Card className="shadow-lg max-w-full">
        <CardHeader>
            <CardTitle>كافة القيود المسجلة ({entries.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-320px)]"> 
            <Table>
              <TableHeader className="sticky top-0 bg-muted/90 backdrop-blur-sm z-10">
                <TableRow>
                  <TableHead className="min-w-[200px] text-right font-headline">الهامش</TableHead>
                  <TableHead className="w-[180px] text-right font-headline">الدائرة</TableHead>
                  <TableHead className="min-w-[200px] text-right font-headline">الفحوى/الموظف</TableHead>
                  <TableHead className="min-w-[250px] text-right font-headline">الموضوع</TableHead>
                  <TableHead className="w-[130px] text-right font-headline">تاريخ الكتاب</TableHead>
                  <TableHead className="w-[150px] text-right font-headline">رقم الكتاب</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {entries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-32 text-center text-muted-foreground text-lg">
                      لا توجد قيود لعرضها. يرجى إضافة قيود من صفحة إدخال البيانات.
                    </TableCell>
                  </TableRow>
                ) : (
                  entries.map((entry) => (
                    <TableRow key={entry.id} className="hover:bg-primary/5">
                      <TableCell className={`text-right max-w-xs truncate ${entry.marginNotes ? 'text-margin-notes font-semibold' : ''}`}>{entry.marginNotes || "-"}</TableCell>
                      <TableCell className="text-right">{entry.department}</TableCell>
                      <TableCell className="text-right max-w-xs truncate">{entry.contentEmployeeName}</TableCell>
                      <TableCell className="text-right">{entry.subject}</TableCell>
                      <TableCell className="text-right">
                        {formatDateSafely(entry.documentDate)}
                      </TableCell>
                      <TableCell className="font-medium text-right">{entry.documentNumber}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              {entries.length > 0 && (
                <TableCaption>نهاية التقرير. إجمالي {entries.length} قيد.</TableCaption>
              )}
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}


    