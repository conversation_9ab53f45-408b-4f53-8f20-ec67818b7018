# **App Name**: Safina Archive

## Core Features:

- Dashboard: Main dashboard for electronic archiving with easy navigation.
- Data Input: Form to input archive data like document number, dates, subject, content, relevant department, and notes.
- Reporting Interface: View and interact with a sortable and filterable report containing all archive entries.
- Filtering: Filter and search records by date, subject, employee name, department, or notes.
- Record Navigation: Navigate through records using buttons for first, previous, next, and last record.
- Record Editing: Functionality to modify or delete entries.
- Theme Selection: Theme customization.

## Style Guidelines:

- Primary color: Saturated blue (#4285F4) to represent trust and organization.
- Background color: Light blue (#E8F0FE), subtly desaturated to maintain focus on the content.
- Accent color: Purple (#673AB7) for interactive elements to give a modern feel.
- Headline font: 'Poppins', a geometric sans-serif (but only for short lines of text). Body font: 'Inter', a grotesque-style sans-serif, to be paired for readability.
- Custom icons to represent different functions, improving clarity and usability.
- Subtle transitions between interfaces and interactive elements for a smooth user experience.
- Right-side vertical menu with distinct buttons that highlight views on the left.