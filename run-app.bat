@echo off
chcp 65001 >nul
title برنامج الأرشفة الإلكترونية - سفينة الأرشيف

echo.
echo ========================================
echo    برنامج الأرشفة الإلكترونية
echo        سفينة الأرشيف
echo ========================================
echo.

echo 🔧 التحقق من متطلبات التشغيل...

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت

:: Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

echo ✅ npm متوفر

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ المكتبات مثبتة

echo.
echo 🏗️  بناء التطبيق...
npm run build:static
if errorlevel 1 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo ✅ تم بناء التطبيق بنجاح

echo.
echo 🚀 تشغيل التطبيق...
echo سيتم فتح التطبيق في المتصفح الافتراضي...
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

npm run start:static

pause
