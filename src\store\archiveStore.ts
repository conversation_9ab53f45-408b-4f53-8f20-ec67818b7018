
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { ArchiveEntry, Settings, Theme, ArchiveState, AppStateValue } from '@/types';

const initialSettings: Settings = {
  theme: 'light',
};

export const useArchiveStore = create<ArchiveState>()(
  persist(
    (set, get) => ({
      entries: [],
      settings: initialSettings,
      appState: 'running' as AppStateValue, // Initialize appState
      addEntry: (entry) => {
        const newEntry = { ...entry, id: crypto.randomUUID() };
        set((state) => ({ entries: [...state.entries, newEntry] }));
      },
      updateEntry: (id, updatedEntry) =>
        set((state) => ({
          entries: state.entries.map((entry) =>
            entry.id === id ? { ...entry, ...updatedEntry } : entry
          ),
        })),
      deleteEntry: (id) =>
        set((state) => ({
          entries: state.entries.filter((entry) => entry.id !== id),
        })),
      setTheme: (theme) =>
        set((state) => ({
          settings: { ...state.settings, theme },
        })),
      setAppState: (newState: AppStateValue) => // Implement setAppState
        set(() => ({ appState: newState })),
      getEntryById: (id: string) => {
        return get().entries.find(entry => entry.id === id);
      }
    }),
    {
      name: 'safina-archive-storage', 
      storage: createJSONStorage(() => localStorage), 
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.entries = state.entries.map(entry => ({
            ...entry,
            documentDate: new Date(entry.documentDate),
          }));
          // Ensure appState is reset to 'running' on rehydration if it was 'exiting'
          state.appState = 'running';
        }
      }
    }
  )
);
