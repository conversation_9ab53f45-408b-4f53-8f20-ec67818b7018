# PowerShell script for running the Archive Application
# برنامج الأرشفة الإلكترونية - سفينة الأرشيف

param(
    [string]$Browser = "",
    [switch]$Help,
    [switch]$BuildOnly,
    [switch]$ServeOnly
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Show-Header {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    برنامج الأرشفة الإلكترونية" -ForegroundColor Yellow
    Write-Host "        سفينة الأرشيف" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

function Show-Help {
    Show-Header
    Write-Host "الاستخدام:" -ForegroundColor Green
    Write-Host "  .\run-app.ps1 [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "الخيارات:" -ForegroundColor Green
    Write-Host "  -Browser <name>    اختيار متصفح محدد (chrome, firefox, edge, opera, brave)" -ForegroundColor White
    Write-Host "  -BuildOnly         بناء التطبيق فقط بدون تشغيل" -ForegroundColor White
    Write-Host "  -ServeOnly         تشغيل التطبيق فقط (يتطلب بناء مسبق)" -ForegroundColor White
    Write-Host "  -Help              عرض هذه المساعدة" -ForegroundColor White
    Write-Host ""
    Write-Host "أمثلة:" -ForegroundColor Green
    Write-Host "  .\run-app.ps1                    # تشغيل عادي" -ForegroundColor Gray
    Write-Host "  .\run-app.ps1 -Browser chrome    # فتح في Chrome" -ForegroundColor Gray
    Write-Host "  .\run-app.ps1 -BuildOnly         # بناء فقط" -ForegroundColor Gray
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Requirements {
    Write-Host "🔧 التحقق من متطلبات التشغيل..." -ForegroundColor Yellow
    
    if (-not (Test-Command "node")) {
        Write-Host "❌ Node.js غير مثبت على النظام" -ForegroundColor Red
        Write-Host "يرجى تثبيت Node.js من: https://nodejs.org" -ForegroundColor Yellow
        return $false
    }
    Write-Host "✅ Node.js مثبت" -ForegroundColor Green
    
    if (-not (Test-Command "npm")) {
        Write-Host "❌ npm غير متوفر" -ForegroundColor Red
        return $false
    }
    Write-Host "✅ npm متوفر" -ForegroundColor Green
    
    if (-not (Test-Path "node_modules")) {
        Write-Host "📦 تثبيت المكتبات المطلوبة..." -ForegroundColor Yellow
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ فشل في تثبيت المكتبات" -ForegroundColor Red
            return $false
        }
    }
    Write-Host "✅ المكتبات مثبتة" -ForegroundColor Green
    
    return $true
}

function Build-Application {
    Write-Host ""
    Write-Host "🏗️  بناء التطبيق..." -ForegroundColor Yellow
    npm run build:static
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في بناء التطبيق" -ForegroundColor Red
        return $false
    }
    Write-Host "✅ تم بناء التطبيق بنجاح" -ForegroundColor Green
    return $true
}

function Start-Application {
    Write-Host ""
    Write-Host "🚀 تشغيل التطبيق..." -ForegroundColor Yellow
    
    if ($Browser) {
        Write-Host "سيتم فتح التطبيق في $Browser..." -ForegroundColor Cyan
    } else {
        Write-Host "سيتم فتح التطبيق في المتصفح الافتراضي..." -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "للإيقاف: اضغط Ctrl+C" -ForegroundColor Gray
    Write-Host ""
    
    if ($Browser) {
        $env:BROWSER = $Browser
    }
    
    npm run start:static
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Show-Header

if (-not (Test-Requirements)) {
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

if ($ServeOnly) {
    if (-not (Test-Path "static")) {
        Write-Host "❌ مجلد static غير موجود. يرجى تشغيل البناء أولاً" -ForegroundColor Red
        Write-Host "استخدم: .\run-app.ps1 -BuildOnly" -ForegroundColor Yellow
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    Start-Application
    exit 0
}

if (-not (Build-Application)) {
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

if (-not $BuildOnly) {
    Start-Application
} else {
    Write-Host ""
    Write-Host "✅ تم بناء التطبيق. يمكنك الآن تشغيله باستخدام:" -ForegroundColor Green
    Write-Host ".\run-app.ps1 -ServeOnly" -ForegroundColor Cyan
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
