
"use client";

import React, { useState, useMemo } from "react";
import { useArchiveStore } from "@/store/archiveStore";
import type { ArchiveEntry } from "@/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, SearchIcon, RotateCcw, FileSpreadsheet } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";
import { PageHeader } from "@/components/shared/PageHeader";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { exportFilteredDataToExcel } from "@/lib/exportUtils"; 

interface Filters {
  dateFrom?: Date;
  dateTo?: Date;
  subject?: string;
  employeeName?: string;
  department?: string;
  marginStatus?: "contains" | "doesNotContain" | "any";
}

export default function FilterPage() {
  const { toast } = useToast();
  const { entries: rawEntries } = useArchiveStore();
  const entries = useMemo(() => rawEntries.map(entry => {
    const dateValue = entry.documentDate;
    const parsedDate = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return {
      ...entry,
      documentDate: parsedDate
    };
  }), [rawEntries]);

  const [filters, setFilters] = useState<Filters>({ marginStatus: "any" });
  const [filteredResults, setFilteredResults] = useState<ArchiveEntry[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleFilterChange = (field: keyof Filters, value: any) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
    setShowResults(false); 
  };

  const applyFilters = () => {
    let results = [...entries];

    if (filters.dateFrom) {
      const dateFromValid = filters.dateFrom instanceof Date && !isNaN(filters.dateFrom.getTime());
      if (dateFromValid) {
        results = results.filter(
          (entry) => entry.documentDate instanceof Date && !isNaN(entry.documentDate.getTime()) && entry.documentDate >= filters.dateFrom!
        );
      }
    }
    if (filters.dateTo) {
      const dateToValid = filters.dateTo instanceof Date && !isNaN(filters.dateTo.getTime());
      if (dateToValid) {
        results = results.filter(
          (entry) => entry.documentDate instanceof Date && !isNaN(entry.documentDate.getTime()) && entry.documentDate <= filters.dateTo!
        );
      }
    }
    if (filters.subject) {
      results = results.filter((entry) =>
        entry.subject.toLowerCase().includes(filters.subject!.toLowerCase())
      );
    }
    if (filters.employeeName) {
      results = results.filter((entry) =>
        entry.contentEmployeeName.toLowerCase().includes(filters.employeeName!.toLowerCase())
      );
    }
    if (filters.department) {
      results = results.filter((entry) =>
        entry.department.toLowerCase().includes(filters.department!.toLowerCase())
      );
    }
    if (filters.marginStatus === "contains") {
      results = results.filter((entry) => entry.marginNotes && entry.marginNotes.trim() !== "");
    } else if (filters.marginStatus === "doesNotContain") {
      results = results.filter((entry) => !entry.marginNotes || entry.marginNotes.trim() === "");
    }

    setFilteredResults(results.sort((a, b) => {
        const timeA = a.documentDate instanceof Date && !isNaN(a.documentDate.getTime()) ? a.documentDate.getTime() : 0;
        const timeB = b.documentDate instanceof Date && !isNaN(b.documentDate.getTime()) ? b.documentDate.getTime() : 0;
        return timeB - timeA;
    }));
    setShowResults(true);
  };

  const clearFilters = () => {
    setFilters({ marginStatus: "any", dateFrom: undefined, dateTo: undefined, subject: "", employeeName: "", department: "" });
    setFilteredResults([]);
    setShowResults(false);
  };

  const handleExportFiltered = () => {
    if (filteredResults.length === 0) {
      toast({
        title: "لا توجد بيانات للتصدير",
        description: "يرجى تطبيق التصفية والحصول على نتائج أولاً.",
        variant: "destructive",
      });
      return;
    }
    try {
      exportFilteredDataToExcel(filteredResults, "نتائج_التصفية.xlsx");
      toast({
        title: "تم التصدير بنجاح",
        description: "تم تنزيل ملف Excel بنتائج التصفية.",
        variant: "default",
      });
    } catch (error) {
      console.error("Error exporting filtered data to Excel:", error);
      toast({
        title: "خطأ في التصدير",
        description: "حدث خطأ أثناء محاولة تصدير بيانات التصفية.",
        variant: "destructive",
      });
    }
  };

  const formatDateSafely = (date: Date | string | undefined, formatString: string = "PPP") => {
    if (!date) return "تاريخ غير محدد";
    const dateObj = date instanceof Date ? date : new Date(date);
    if (dateObj instanceof Date && !isNaN(dateObj.getTime())) {
      return format(dateObj, formatString, { locale: arSA });
    }
    return "تاريخ غير صالح";
  };

  return (
    <div className="space-y-6">
      <PageHeader title="التصفية المتقدمة" description="قم بتحديد معايير البحث لتصفية قيود الأرشيف." showBackButton>
         <Button variant="outline" size="lg" className="glowing-button" onClick={handleExportFiltered} disabled={!showResults || filteredResults.length === 0}>
          <FileSpreadsheet className="ml-2 h-5 w-5" />
          تصدير النتائج إلى Excel
        </Button>
      </PageHeader>
      
      <Card className="max-w-7xl mx-auto shadow-lg">
        <CardHeader>
          <CardTitle>معايير التصفية</CardTitle>
          <CardDescription>أدخل واحداً أو أكثر من المعايير التالية للبحث الدقيق في الأرشيف.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-6">
            <div>
              <Label htmlFor="dateFrom" className="text-base">من تاريخ</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateFrom"
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal mt-1 text-base py-2.5 h-auto glowing-input",
                      !filters.dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {filters.dateFrom ? formatDateSafely(filters.dateFrom) : <span>اختر تاريخًا</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    mode="single"
                    selected={filters.dateFrom}
                    onSelect={(date) => handleFilterChange("dateFrom", date)}
                    initialFocus
                    locale={arSA}
                    dir="rtl"
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label htmlFor="dateTo" className="text-base">إلى تاريخ</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateTo"
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal mt-1 text-base py-2.5 h-auto glowing-input",
                      !filters.dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {filters.dateTo ? formatDateSafely(filters.dateTo) : <span>اختر تاريخًا</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    mode="single"
                    selected={filters.dateTo}
                    onSelect={(date) => handleFilterChange("dateTo", date)}
                    initialFocus
                    locale={arSA}
                    dir="rtl"
                  />
                </PopoverContent>
              </Popover>
            </div>
             <div>
              <Label htmlFor="marginStatus" className="text-base">حالة الهامش</Label>
               <Select
                value={filters.marginStatus}
                onValueChange={(value) => handleFilterChange("marginStatus", value as Filters["marginStatus"])}
                dir="rtl"
              >
                <SelectTrigger id="marginStatus" className="w-full mt-1 glowing-input text-base py-2.5 h-auto">
                  <SelectValue placeholder="اختر حالة الهامش" />
                </SelectTrigger>
                <SelectContent position="popper" sideOffset={5} align="end">
                  <SelectItem value="any" className="text-base">الكل</SelectItem>
                  <SelectItem value="contains" className="text-base">يحتوي على هامش</SelectItem>
                  <SelectItem value="doesNotContain" className="text-base">لا يحتوي على هامش</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-6">
            <div>
              <Label htmlFor="subject" className="text-base">الموضوع</Label>
              <Input
                id="subject"
                value={filters.subject || ""}
                onChange={(e) => handleFilterChange("subject", e.target.value)}
                placeholder="كلمة مفتاحية من الموضوع"
                className="mt-1 glowing-input text-base py-2.5"
              />
            </div>

            <div>
              <Label htmlFor="employeeName" className="text-base">اسم الموظف المعني</Label>
              <Input
                id="employeeName"
                value={filters.employeeName || ""}
                onChange={(e) => handleFilterChange("employeeName", e.target.value)}
                placeholder="جزء من اسم الموظف"
                className="mt-1 glowing-input text-base py-2.5"
              />
            </div>
            
            <div>
              <Label htmlFor="department" className="text-base">الدائرة المعنية</Label>
              <Input
                id="department"
                value={filters.department || ""}
                onChange={(e) => handleFilterChange("department", e.target.value)}
                placeholder="جزء من اسم الدائرة"
                className="mt-1 glowing-input text-base py-2.5"
              />
            </div>
          </div>


          <div className="flex gap-4 pt-6">
            <Button onClick={applyFilters} size="lg" className="glowing-button text-lg">
              <SearchIcon className="ml-2 h-5 w-5" />
              تطبيق التصفية
            </Button>
            <Button variant="outline" size="lg" onClick={clearFilters} className="text-lg">
              <RotateCcw className="ml-2 h-5 w-5" />
              مسح المعايير
            </Button>
          </div>
        </CardContent>
      </Card>

      {showResults && (
        <Card className="mt-8 max-w-full mx-auto shadow-lg">
          <CardHeader>
            <CardTitle>نتائج التصفية ({filteredResults.length})</CardTitle>
             {filteredResults.length === 0 && (
                <CardDescription>لم يتم العثور على قيود تطابق معايير البحث المدخلة.</CardDescription>
            )}
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[500px]"> 
              <Table>
                <TableHeader className="sticky top-0 bg-muted/90 backdrop-blur-sm z-10">
                  <TableRow>
                    <TableHead className="w-[150px] text-right">رقم الكتاب</TableHead>
                    <TableHead className="w-[130px] text-right">التاريخ</TableHead>
                    <TableHead className="min-w-[250px] text-right">الموضوع</TableHead>
                    <TableHead className="min-w-[200px] text-right">الفحوى/الموظف</TableHead>
                    <TableHead className="w-[180px] text-right">الدائرة</TableHead>
                    <TableHead className="min-w-[200px] text-right">الهامش</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredResults.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-32 text-center text-muted-foreground text-lg">
                        لا توجد نتائج تطابق معايير البحث.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredResults.map((entry) => (
                      <TableRow key={entry.id} className="hover:bg-primary/5">
                        <TableCell className="font-medium text-right">{entry.documentNumber}</TableCell>
                        <TableCell className="text-right">
                          {formatDateSafely(entry.documentDate, "dd MMMM yyyy")}
                        </TableCell>
                        <TableCell className="text-right">{entry.subject}</TableCell>
                        <TableCell className="text-right max-w-xs truncate">{entry.contentEmployeeName}</TableCell>
                        <TableCell className="text-right">{entry.department}</TableCell>
                        <TableCell className={`text-right max-w-xs truncate ${entry.marginNotes ? 'text-margin-notes font-semibold' : ''}`}>{entry.marginNotes || "-"}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
                {filteredResults.length > 0 && (
                    <TableCaption>تم عرض {filteredResults.length} نتيجة مطابقة لمعايير التصفية.</TableCaption>
                )}
              </Table>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}


    