(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,r:()=>d});var a=r(5155),s=r(2115),n=r(9708),o=r(2085),l=r(9434);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:i=!1,...c}=e,f=i?n.DX:"button";return(0,a.jsx)(f,{className:(0,l.cn)(d({variant:s,size:o,className:r})),ref:t,...c})});i.displayName="Button"},2383:()=>{},3686:()=>{},4592:(e,t,r)=>{"use strict";r.d(t,{b:()=>l,c:()=>d});var a=r(3925),s=r(3013),n=r(5471);let o=e=>{if(!e)return"تاريخ غير محدد";let t=e instanceof Date?e:new Date(e);return t instanceof Date&&!isNaN(t.getTime())?(0,s.GP)(t,"dd MMMM yyyy",{locale:n.G}):"تاريخ غير صالح"};function l(e,t){if(!e||0===e.length){console.warn("No data to export.");return}let r=["الهامش","الدائرة","الفحوى/الموظف","الموضوع","تاريخ الكتاب","رقم الكتاب"],s=e.map(e=>[e.marginNotes||"-",e.department,e.contentEmployeeName,e.subject,o(e.documentDate),e.documentNumber]),n=a.Wp.aoa_to_sheet([r,...s]),l=r.map((e,t)=>{let a=0;return[r,...s].forEach(e=>{let r=e[t]?String(e[t]):"";r.length>a&&(a=r.length)}),{wch:Math.min(Math.max(a,10),50)}});n["!cols"]=l;let d=a.Wp.book_new();a.Wp.book_append_sheet(d,n,"البيانات"),a._h(d,t.endsWith(".xlsx")?t:"".concat(t,".xlsx"))}function d(e,t){if(!e||0===e.length){console.warn("No data to export.");return}let r=["رقم الكتاب","تاريخ الكتاب","الموضوع","الفحوى/الموظف","الدائرة","الهامش"],s=e.map(e=>[e.documentNumber,o(e.documentDate),e.subject,e.contentEmployeeName,e.department,e.marginNotes||"-"]),n=a.Wp.aoa_to_sheet([r,...s]),l=r.map((e,t)=>{let a=0;return[r,...s].forEach(e=>{let r=e[t]?String(e[t]):"";r.length>a&&(a=r.length)}),{wch:Math.min(Math.max(a,10),50)}});n["!cols"]=l;let d=a.Wp.book_new();a.Wp.book_append_sheet(d,n,"نتائج التصفية"),a._h(d,t.endsWith(".xlsx")?t:"".concat(t,".xlsx"))}},5127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>l,BF:()=>d,Hj:()=>i,XI:()=>o,nA:()=>f,nd:()=>c,r6:()=>u});var a=r(5155),s=r(2115),n=r(9434);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",r),...s})})});o.displayName="Table";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",r),...s})});l.displayName="TableHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",r),...s})});d.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});i.displayName="TableRow";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});c.displayName="TableHead";let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});f.displayName="TableCell";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",r),...s})});u.displayName="TableCaption"},6424:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var a=r(5155),s=r(2115),n=r(7655),o=r(9434);let l=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(n.bL,{ref:t,className:(0,o.cn)("relative overflow-hidden",r),...l,children:[(0,a.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(d,{}),(0,a.jsx)(n.OK,{})]})});l.displayName=n.bL.displayName;let d=s.forwardRef((e,t)=>{let{className:r,orientation:s="vertical",...l}=e;return(0,a.jsx)(n.VM,{ref:t,orientation:s,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...l,children:(0,a.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})})});d.displayName=n.VM.displayName},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>d,Zp:()=>o,aR:()=>l});var a=r(5155),s=r(2115),n=r(9434);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});o.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});i.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},6976:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var a=r(5155),s=r(6874),n=r.n(s),o=r(285),l=r(9968);function d(e){let{title:t,description:r,showBackButton:s=!1,backButtonHref:d="/dashboard",backButtonText:i="العودة إلى الرئيسية",children:c}=e;return(0,a.jsxs)("div",{className:"mb-8 pb-6 border-b border-border/50",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center justify-between gap-4",children:[(0,a.jsx)("div",{className:"page-header-title-bg",children:(0,a.jsx)("h1",{className:"text-4xl font-headline font-extrabold tracking-tight page-header-title-text",children:t})}),(0,a.jsxs)("div",{className:"flex items-center gap-3 self-start md:self-center",children:[c,s&&(0,a.jsx)(n(),{href:d,passHref:!0,children:(0,a.jsxs)(o.$,{variant:"outline",size:"lg",className:"glowing-button",children:[(0,a.jsx)(l.A,{className:"ml-2 h-5 w-5"}),i]})})]})]}),r&&(0,a.jsx)("p",{className:"text-muted-foreground mt-3 text-lg",children:r})]})}},7481:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u});var a=r(2115);let s=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],i={toasts:[]};function c(e){i=l(i,e),d.forEach(e=>{e(i)})}function f(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=a.useState(i);return a.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:f,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},9404:(e,t,r)=>{"use strict";r.d(t,{f:()=>o});var a=r(8693),s=r(6786);let n={theme:"light"},o=(0,a.vt)()((0,s.Zr)((e,t)=>({entries:[],settings:n,appState:"running",addEntry:t=>{let r={...t,id:crypto.randomUUID()};e(e=>({entries:[...e.entries,r]}))},updateEntry:(t,r)=>e(e=>({entries:e.entries.map(e=>e.id===t?{...e,...r}:e)})),deleteEntry:t=>e(e=>({entries:e.entries.filter(e=>e.id!==t)})),setTheme:t=>e(e=>({settings:{...e.settings,theme:t}})),setAppState:t=>e(()=>({appState:t})),getEntryById:e=>t().entries.find(t=>t.id===e)}),{name:"safina-archive-storage",storage:(0,s.KU)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&(e.entries=e.entries.map(e=>({...e,documentDate:new Date(e.documentDate)})),e.appState="running")}}))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}}]);