(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[741],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var a=r(5155),s=r(2115),n=r(9708),l=r(2085),i=r(9434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:s,size:l,className:r})),ref:t,...c})});d.displayName="Button"},750:(e,t,r)=>{Promise.resolve().then(r.bind(r,5518))},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var a=r(5155),s=r(2115),n=r(9434);let l=s.forwardRef((e,t)=>{let{className:r,type:s,...l}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});l.displayName="Input"},4636:(e,t,r)=>{"use strict";r.d(t,{AM:()=>i,Wv:()=>o,hl:()=>d});var a=r(5155),s=r(2115),n=r(547),l=r(9434);let i=n.bL,o=n.l9,d=s.forwardRef((e,t)=>{let{className:r,align:s="center",sideOffset:i=4,...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,align:s,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});d.displayName=n.UC.displayName},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var a=r(5155),s=r(2115),n=r(968),l=r(2085),i=r(9434);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,i.cn)(o(),r),...s})});d.displayName=n.b.displayName},5511:(e,t,r)=>{"use strict";r.d(t,{V:()=>d});var a=r(5155);r(2115);var s=r(965),n=r(3158),l=r(3900),i=r(9434),o=r(285);function d(e){let{className:t,classNames:r,showOutsideDays:d=!0,...c}=e;return(0,a.jsx)(l.hv,{showOutsideDays:d,className:(0,i.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,i.cn)((0,o.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,i.cn)((0,o.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...r},components:{IconLeft:e=>{let{className:t,...r}=e;return(0,a.jsx)(s.A,{className:(0,i.cn)("h-4 w-4",t),...r})},IconRight:e=>{let{className:t,...r}=e;return(0,a.jsx)(n.A,{className:(0,i.cn)("h-4 w-4",t),...r})}},...c})}d.displayName="Calendar"},5518:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>F});var a=r(5155),s=r(221),n=r(2177),l=r(5594),i=r(285),o=r(2115),d=r(9708),c=r(9434),m=r(5057);let u=n.Op,f=o.createContext({}),x=e=>{let{...t}=e;return(0,a.jsx)(f.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},p=()=>{let e=o.useContext(f),t=o.useContext(g),{getFieldState:r,formState:a}=(0,n.xW)(),s=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},g=o.createContext({}),h=o.forwardRef((e,t)=>{let{className:r,...s}=e,n=o.useId();return(0,a.jsx)(g.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,c.cn)("space-y-2",r),...s})})});h.displayName="FormItem";let b=o.forwardRef((e,t)=>{let{className:r,...s}=e,{error:n,formItemId:l}=p();return(0,a.jsx)(m.J,{ref:t,className:(0,c.cn)(n&&"text-destructive",r),htmlFor:l,...s})});b.displayName="FormLabel";let v=o.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:n,formDescriptionId:l,formMessageId:i}=p();return(0,a.jsx)(d.DX,{ref:t,id:n,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...r})});v.displayName="FormControl";let j=o.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:n}=p();return(0,a.jsx)("p",{ref:t,id:n,className:(0,c.cn)("text-sm text-muted-foreground",r),...s})});j.displayName="FormDescription";let y=o.forwardRef((e,t)=>{var r;let{className:s,children:n,...l}=e,{error:i,formMessageId:o}=p(),d=i?String(null!==(r=null==i?void 0:i.message)&&void 0!==r?r:""):n;return d?(0,a.jsx)("p",{ref:t,id:o,className:(0,c.cn)("text-sm font-medium text-destructive",s),...l,children:d}):null});y.displayName="FormMessage";var N=r(2523);let w=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});w.displayName="Textarea";var _=r(5511),S=r(4636),T=r(1920),A=r(9119),D=r(3013),E=r(5471),I=r(9404),R=r(7481),C=r(6976),O=r(6695);let k=l.Ik({documentNumber:l.Yj().min(1,"رقم الكتاب مطلوب"),documentDate:l.p6({required_error:"تاريخ الكتاب مطلوب"}),subject:l.Yj().min(1,"الموضوع مطلوب"),contentEmployeeName:l.Yj().min(1,"الفحوى أو اسم الموظف مطلوب"),department:l.Yj().min(1,"الدائرة المعنية مطلوبة"),marginNotes:l.Yj().optional()});function F(){let e=(0,I.f)(e=>e.addEntry),{toast:t}=(0,R.dj)(),r=(0,n.mN)({resolver:(0,s.u)(k),defaultValues:{documentNumber:"",subject:"",contentEmployeeName:"",department:"",marginNotes:"",documentDate:void 0}});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(C.z,{title:"إدخال البيانات",description:"قم بإدخال تفاصيل المستند الجديد هنا."}),(0,a.jsxs)(O.Zp,{className:"max-w-5xl mx-auto shadow-lg",children:[(0,a.jsx)(O.aR,{children:(0,a.jsx)(O.ZB,{children:"نموذج قيد جديد"})}),(0,a.jsx)(O.Wu,{children:(0,a.jsx)(u,{...r,children:(0,a.jsxs)("form",{onSubmit:r.handleSubmit(function(a){e({...a,marginNotes:a.marginNotes||""}),t({title:"تم الحفظ",description:"تم حفظ القيد الجديد في أرشيف البرنامج.",variant:"default"}),r.reset()}),className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsx)(x,{control:r.control,name:"documentNumber",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(b,{className:"text-lg",children:"رقم الكتاب"}),(0,a.jsx)(v,{children:(0,a.jsx)(N.p,{placeholder:"مثال: 123/أ/2023",...t,className:"glowing-input text-base py-2.5"})}),(0,a.jsx)(y,{})]})}}),(0,a.jsx)(x,{control:r.control,name:"documentDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{className:"flex flex-col",children:[(0,a.jsx)(b,{className:"text-lg",children:"تاريخ الكتاب"}),(0,a.jsxs)(S.AM,{children:[(0,a.jsx)(S.Wv,{asChild:!0,children:(0,a.jsx)(v,{children:(0,a.jsxs)(i.$,{variant:"outline",className:(0,c.cn)("w-full pl-3 text-left font-normal text-base py-2.5 h-auto glowing-input",!t.value&&"text-muted-foreground"),children:[t.value?(0,D.GP)(t.value,"PPP",{locale:E.G}):(0,a.jsx)("span",{children:"اختر تاريخًا"}),(0,a.jsx)(T.A,{className:"mr-auto h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(S.hl,{className:"w-auto p-0",align:"end",children:(0,a.jsx)(_.V,{mode:"single",selected:t.value,onSelect:t.onChange,disabled:e=>e>new Date||e<new Date("1900-01-01"),initialFocus:!0,dir:"rtl",locale:E.G})})]}),(0,a.jsx)(y,{})]})}})]}),(0,a.jsx)(x,{control:r.control,name:"subject",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(b,{className:"text-lg",children:"الموضوع"}),(0,a.jsx)(v,{children:(0,a.jsx)(N.p,{placeholder:"موضوع الكتاب",...t,className:"glowing-input text-base py-2.5"})}),(0,a.jsx)(y,{})]})}}),(0,a.jsx)(x,{control:r.control,name:"contentEmployeeName",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(b,{className:"text-lg",children:"الفحوى أو اسم الموظف المعني"}),(0,a.jsx)(v,{children:(0,a.jsx)(w,{placeholder:"أدخل فحوى الكتاب أو اسم الموظف المسؤول...",className:"resize-y min-h-[120px] glowing-input text-base",...t})}),(0,a.jsx)(j,{children:"يمكنك إدخال نص طويل هنا."}),(0,a.jsx)(y,{})]})}}),(0,a.jsx)(x,{control:r.control,name:"department",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(b,{className:"text-lg",children:"الدائرة المعنية"}),(0,a.jsx)(v,{children:(0,a.jsx)(N.p,{placeholder:"اسم الدائرة أو القسم",...t,className:"glowing-input text-base py-2.5"})}),(0,a.jsx)(y,{})]})}}),(0,a.jsx)(x,{control:r.control,name:"marginNotes",render:e=>{let{field:t}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(b,{className:"text-lg",children:"الهامش (ملاحظات إضافية)"}),(0,a.jsx)(v,{children:(0,a.jsx)(w,{placeholder:"أضف أي ملاحظات أو تعليقات هامشية هنا...",className:"resize-y min-h-[100px] glowing-input text-margin-notes text-base",...t})}),(0,a.jsx)(y,{})]})}}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(i.$,{type:"submit",size:"lg",className:"glowing-button text-lg py-3 px-8",children:[(0,a.jsx)(A.A,{className:"ml-2 h-5 w-5"}),"الحفظ"]})})]})})})]})]})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var a=r(5155),s=r(2115),n=r(9434);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});l.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},6976:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var a=r(5155),s=r(6874),n=r.n(s),l=r(285),i=r(9968);function o(e){let{title:t,description:r,showBackButton:s=!1,backButtonHref:o="/dashboard",backButtonText:d="العودة إلى الرئيسية",children:c}=e;return(0,a.jsxs)("div",{className:"mb-8 pb-6 border-b border-border/50",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center justify-between gap-4",children:[(0,a.jsx)("div",{className:"page-header-title-bg",children:(0,a.jsx)("h1",{className:"text-4xl font-headline font-extrabold tracking-tight page-header-title-text",children:t})}),(0,a.jsxs)("div",{className:"flex items-center gap-3 self-start md:self-center",children:[c,s&&(0,a.jsx)(n(),{href:o,passHref:!0,children:(0,a.jsxs)(l.$,{variant:"outline",size:"lg",className:"glowing-button",children:[(0,a.jsx)(i.A,{className:"ml-2 h-5 w-5"}),d]})})]})]}),r&&(0,a.jsx)("p",{className:"text-muted-foreground mt-3 text-lg",children:r})]})}},7481:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u});var a=r(2115);let s=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=a.useState(d);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},9404:(e,t,r)=>{"use strict";r.d(t,{f:()=>l});var a=r(8693),s=r(6786);let n={theme:"light"},l=(0,a.vt)()((0,s.Zr)((e,t)=>({entries:[],settings:n,appState:"running",addEntry:t=>{let r={...t,id:crypto.randomUUID()};e(e=>({entries:[...e.entries,r]}))},updateEntry:(t,r)=>e(e=>({entries:e.entries.map(e=>e.id===t?{...e,...r}:e)})),deleteEntry:t=>e(e=>({entries:e.entries.filter(e=>e.id!==t)})),setTheme:t=>e(e=>({settings:{...e.settings,theme:t}})),setAppState:t=>e(()=>({appState:t})),getEntryById:e=>t().entries.find(t=>t.id===e)}),{name:"safina-archive-storage",storage:(0,s.KU)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&(e.entries=e.entries.map(e=>({...e,documentDate:new Date(e.documentDate)})),e.appState="running")}}))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[668,793,239,26,756,152,831,596,441,684,358],()=>t(750)),_N_E=e.O()}]);