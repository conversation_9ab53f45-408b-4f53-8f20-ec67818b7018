const fs = require('fs');
const path = require('path');

// Function to copy files recursively
function copyRecursive(src, dest) {
  const exists = fs.existsSync(src);
  const stats = exists && fs.statSync(src);
  const isDirectory = exists && stats.isDirectory();
  
  if (isDirectory) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    fs.readdirSync(src).forEach(function(childItemName) {
      copyRecursive(path.join(src, childItemName), path.join(dest, childItemName));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

// Function to fix HTML files for local serving
function fixHtmlFiles(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      fixHtmlFiles(filePath);
    } else if (file.endsWith('.html')) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Fix asset paths for local serving
      content = content.replace(/href="\/_next\//g, 'href="./_next/');
      content = content.replace(/src="\/_next\//g, 'src="./_next/');
      content = content.replace(/href="\/favicon\.ico"/g, 'href="./favicon.ico"');
      
      // Fix internal links
      content = content.replace(/href="\/([^"]*?)"/g, (match, p1) => {
        if (p1.startsWith('_next/') || p1.startsWith('favicon')) {
          return match;
        }
        return `href="./${p1}.html"`;
      });
      
      fs.writeFileSync(filePath, content);
      console.log(`Fixed paths in: ${filePath}`);
    }
  });
}

console.log('Copying static files...');

// Copy the out directory to a new static directory
const outDir = path.join(__dirname, '..', 'out');
const staticDir = path.join(__dirname, '..', 'static');

if (fs.existsSync(staticDir)) {
  fs.rmSync(staticDir, { recursive: true, force: true });
}

copyRecursive(outDir, staticDir);

// Fix HTML files for local serving
console.log('Fixing HTML files for local serving...');
fixHtmlFiles(staticDir);

console.log('Static files prepared successfully!');
console.log('Files are ready in the "static" directory');
