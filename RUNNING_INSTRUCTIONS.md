# تعليمات تشغيل برنامج الأرشفة الإلكترونية - سفينة الأرشيف

## 🚀 طرق التشغيل المختلفة

### 1. التشغيل السريع على Windows

#### الطريقة الأولى: ملف Batch
```bash
# انقر مرتين على الملف
run-app.bat
```

#### الطريقة الثانية: PowerShell (مستحسن)
```powershell
# تشغيل عادي
.\run-app.ps1

# تشغيل مع متصفح محدد
.\run-app.ps1 -Browser chrome
.\run-app.ps1 -Browser firefox
.\run-app.ps1 -Browser edge

# بناء فقط
.\run-app.ps1 -BuildOnly

# تشغيل فقط (بعد البناء)
.\run-app.ps1 -ServeOnly

# عرض المساعدة
.\run-app.ps1 -Help
```

### 2. التشغيل باستخدام npm

#### بناء وتشغيل كامل
```bash
npm run run:app
```

#### بناء الملفات الثابتة
```bash
npm run build:static
```

#### تشغيل الخادم المحلي
```bash
npm run start:static
```

#### فتح في المتصفح
```bash
npm run open:browser
```

### 3. التشغيل المحلي المباشر

#### فتح الملف المحلي
```bash
# افتح الملف في المتصفح
local-index.html
```

## 🔧 متطلبات التشغيل

- **Node.js** (الإصدار 18 أو أحدث)
- **npm** (يأتي مع Node.js)
- **متصفح ويب حديث** (Chrome, Firefox, Edge, Safari)

## 📁 هيكل الملفات

```
├── run-app.bat              # سكريبت Windows Batch
├── run-app.ps1              # سكريبت PowerShell المتقدم
├── local-index.html         # صفحة التشغيل المحلي
├── scripts/                 # مجلد السكريبتات
│   ├── copy-static.js       # نسخ وإصلاح الملفات الثابتة
│   ├── serve-static.js      # خادم الملفات الثابتة
│   └── open-browser.js      # فتح المتصفح
├── static/                  # الملفات المبنية (بعد البناء)
└── out/                     # ملفات Next.js المبنية
```

## 🛠️ حل المشاكل الشائعة

### مشكلة الصفحة البيضاء
- **السبب**: مسارات الملفات النسبية في static export
- **الحل**: استخدم أحد طرق التشغيل المذكورة أعلاه

### مشكلة عدم وجود Node.js
```bash
# تحميل وتثبيت Node.js من
https://nodejs.org
```

### مشكلة عدم وجود المكتبات
```bash
npm install
```

### مشكلة عدم فتح المتصفح تلقائياً
- تأكد من أن المتصفح مثبت
- جرب فتح الرابط يدوياً: `http://localhost:3000`

## 🌐 المتصفحات المدعومة

- **Google Chrome** ✅
- **Mozilla Firefox** ✅
- **Microsoft Edge** ✅
- **Opera** ✅
- **Brave** ✅
- **Safari** ✅ (على macOS)

## 📱 التوافق مع الأنظمة

- **Windows 10/11** ✅ (مدعوم بالكامل)
- **Windows 8.1** ✅
- **Windows 7** ⚠️ (يتطلب Node.js متوافق)
- **macOS** ✅
- **Linux** ✅

## 🔍 اختبار التشغيل

1. **اختبار البناء**:
   ```bash
   npm run build:static
   ```

2. **اختبار الخادم**:
   ```bash
   npm run start:static
   ```

3. **اختبار المتصفح**:
   - افتح `http://localhost:3000`
   - يجب أن ترى واجهة التطبيق

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل:

1. تأكد من تثبيت Node.js
2. تأكد من تشغيل `npm install`
3. جرب `npm run build:static` أولاً
4. استخدم PowerShell كمدير للحصول على صلاحيات أكثر

## 👨‍💻 معلومات المطور

- **المطور**: المحاسب المبرمج علي عاجل خشان المحنة
- **الإصدار**: 2.0.0
- **التاريخ**: 2025

---

**ملاحظة**: هذا التطبيق مصمم للعمل على أنظمة Windows بشكل أساسي، مع دعم للأنظمة الأخرى.
