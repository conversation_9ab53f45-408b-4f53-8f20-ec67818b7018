
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 86% 95.3%; /* Light Blue #E8F0FE */
    --foreground: 220 25% 25%; /* Darker Blue-Gray for text */
    --card: 220 86% 97%;
    --card-foreground: 220 25% 25%;
    --popover: 220 86% 95.3%;
    --popover-foreground: 220 25% 25%;
    --primary: 220 89% 61%; /* Saturated Blue #4285F4 */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 220 70% 85%; 
    --secondary-foreground: 220 89% 30%;
    --muted: 220 50% 90%;
    --muted-foreground: 220 25% 45%;
    --accent: 265 53% 47%; /* Purple #673AB7 */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 30% 85%;
    --input: 220 30% 88%;
    --ring: 220 89% 61%; /* Primary color for rings */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    --sidebar-background: 220 60% 92%;
    --sidebar-foreground: 220 25% 15%;
    --sidebar-primary: 220 89% 61%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 265 53% 47%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 30% 80%;
    --sidebar-ring: 220 89% 61%;

    --bright-green: 120 100% 35%;
    --title-text-purple: 270 80% 60%;
    --title-text-blue: 210 90% 55%;
    --title-bg-red: 0 90% 60%;
    --title-bg-orange: 30 95% 55%;
    --title-bg-yellow: 50 100% 50%;
  }

  .dark {
    --background: 220 30% 10%;
    --foreground: 220 86% 95.3%;
    --card: 220 30% 12%;
    --card-foreground: 220 86% 95.3%;
    --popover: 220 30% 10%;
    --popover-foreground: 220 86% 95.3%;
    --primary: 220 89% 61%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 50% 30%;
    --secondary-foreground: 220 86% 95.3%;
    --muted: 220 30% 15%;
    --muted-foreground: 220 50% 70%;
    --accent: 265 53% 55%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 30% 25%;
    --input: 220 30% 22%;
    --ring: 220 89% 61%;

    --sidebar-background: 220 30% 8%;
    --sidebar-foreground: 220 86% 92%;
    --sidebar-primary: 220 89% 61%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 265 53% 55%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 30% 20%;
    --sidebar-ring: 220 89% 61%;

    --bright-green: 120 100% 60%;
    --title-text-purple: 270 90% 70%;
    --title-text-blue: 210 100% 65%;
    --title-bg-red: 0 100% 65%;
    --title-bg-orange: 30 100% 60%;
    --title-bg-yellow: 50 100% 55%;
  }

  .theme-ocean-sunset {
    --background: 210 70% 15%; 
    --foreground: 210 80% 90%; 
    --card: 210 60% 20%;
    --card-foreground: 210 80% 90%;
    --popover: 210 70% 15%;
    --popover-foreground: 210 80% 90%;
    --primary: 30 100% 60%;  
    --primary-foreground: 210 30% 10%;
    --secondary: 270 60% 40%; 
    --secondary-foreground: 270 80% 90%;
    --muted: 210 50% 25%;
    --muted-foreground: 210 60% 70%;
    --accent: 190 100% 50%; 
    --accent-foreground: 210 30% 5%;
    --destructive: 0 80% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 50% 30%;
    --input: 210 50% 28%;
    --ring: 30 100% 60%;

    --sidebar-background: 210 70% 12%;
    --sidebar-foreground: 210 80% 85%;
    --sidebar-primary: 30 100% 60%;
    --sidebar-primary-foreground: 210 30% 10%;
    --sidebar-accent: 190 100% 50%;
    --sidebar-accent-foreground: 210 30% 5%;
    --sidebar-border: 210 50% 25%;
    --sidebar-ring: 30 100% 60%;

    --bright-green: 120 100% 60%;
    --title-text-purple: 270 90% 70%;
    --title-text-blue: 190 100% 65%;
    --title-bg-red: 0 100% 65%;
    --title-bg-orange: 30 100% 60%;
    --title-bg-yellow: 50 100% 55%;
  }

  .theme-fiery-passion {
    --background: 15 60% 10%; 
    --foreground: 30 100% 90%; 
    --card: 15 60% 15%;
    --card-foreground: 30 100% 90%;
    --popover: 15 60% 10%;
    --popover-foreground: 30 100% 90%;
    --primary: 0 100% 50%; 
    --primary-foreground: 0 0% 100%;
    --secondary: 30 100% 50%; 
    --secondary-foreground: 15 30% 10%;
    --muted: 15 40% 20%;
    --muted-foreground: 30 80% 70%;
    --accent: 45 100% 50%; 
    --accent-foreground: 15 30% 5%;
    --destructive: 340 90% 50%; 
    --destructive-foreground: 0 0% 100%;
    --border: 15 50% 25%;
    --input: 15 50% 22%;
    --ring: 0 100% 50%;

    --sidebar-background: 15 60% 8%;
    --sidebar-foreground: 30 100% 85%;
    --sidebar-primary: 0 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 45 100% 50%;
    --sidebar-accent-foreground: 15 30% 5%;
    --sidebar-border: 15 50% 20%;
    --sidebar-ring: 0 100% 50%;

    --bright-green: 100 100% 55%;
    --title-text-purple: 300 100% 70%; 
    --title-text-blue: 210 100% 60%; 
    --title-bg-red: 0 100% 50%;
    --title-bg-orange: 30 100% 50%;
    --title-bg-yellow: 45 100% 50%;
  }

  .theme-electric-pop {
    --background: 270 50% 96%; 
    --foreground: 270 40% 20%; 
    --card: 0 0% 100%; 
    --card-foreground: 270 40% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 270 40% 20%;
    --primary: 330 100% 55%; 
    --primary-foreground: 0 0% 100%;
    --secondary: 210 100% 60%; 
    --secondary-foreground: 0 0% 100%;
    --muted: 270 50% 90%;
    --muted-foreground: 270 30% 40%;
    --accent: 180 100% 45%; 
    --accent-foreground: 0 0% 100%;
    --destructive: 0 90% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 270 50% 85%;
    --input: 270 50% 92%;
    --ring: 330 100% 55%;

    --sidebar-background: 270 60% 92%; 
    --sidebar-foreground: 270 40% 15%;
    --sidebar-primary: 330 100% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 180 100% 45%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 270 50% 80%;
    --sidebar-ring: 330 100% 55%;

    --bright-green: 120 90% 45%;
    --title-text-purple: 270 100% 60%; 
    --title-text-blue: 210 100% 55%;   
    --title-bg-red: 0 100% 60%;
    --title-bg-orange: 25 100% 58%;
    --title-bg-yellow: 50 100% 58%;
  }

  .theme-ocean-sunset body, .theme-fiery-passion body, .theme-electric-pop body {
    background-size: 200% 200% !important;
    animation: vibrantBodyBackground 15s ease infinite;
  }

  .theme-ocean-sunset body {
     background-image: linear-gradient(135deg, hsl(210, 70%, 25%), hsl(270, 60%, 30%), hsl(30, 100%, 50%), hsl(190, 100%, 40%)) !important;
  }
  .theme-fiery-passion body {
    background-image: linear-gradient(135deg, hsl(15, 80%, 30%), hsl(0, 100%, 40%), hsl(30, 100%, 45%), hsl(45, 100%, 40%)) !important;
  }
  .theme-electric-pop body {
     background-image: linear-gradient(135deg, hsl(330, 100%, 70%), hsl(210, 100%, 70%), hsl(270, 100%, 75%), hsl(180, 100%, 60%)) !important;
  }
}

@keyframes vibrantBodyBackground {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-body; 
    direction: rtl; 
  }
}

.glowing-button {
  @apply shadow-[0_0_5px_hsl(var(--accent)),_0_0_10px_hsl(var(--accent)),_0_0_15px_hsl(var(--accent)),_0_0_20px_hsl(var(--primary))];
  transition: box-shadow 0.3s ease-in-out;
}
.glowing-button:hover, .glowing-button:focus {
  @apply shadow-[0_0_8px_hsl(var(--accent)),_0_0_15px_hsl(var(--accent)),_0_0_25px_hsl(var(--primary)),_0_0_35px_hsl(var(--primary))];
}

.glowing-input {
  @apply shadow-[0_0_2px_hsl(var(--accent)_/_0.7),_0_0_4px_hsl(var(--accent)_/_0.5),_0_0_6px_hsl(var(--primary)_/_0.5)];
  transition: box-shadow 0.3s ease-in-out;
}
.glowing-input:focus-within, .glowing-input:focus {
   @apply shadow-[0_0_4px_hsl(var(--accent)),_0_0_8px_hsl(var(--accent)),_0_0_12px_hsl(var(--primary)),_0_0_16px_hsl(var(--primary))];
}

.text-margin-notes {
  color: hsl(var(--bright-green));
}

.page-header-title-text {
  background-image: linear-gradient(to right, hsl(var(--title-text-purple)), hsl(var(--title-text-blue)));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.page-header-title-bg {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  background-image: linear-gradient(to right, hsl(var(--title-bg-red)), hsl(var(--title-bg-orange)), hsl(var(--title-bg-yellow)));
  box-shadow: 0 2px 10px hsla(var(--title-bg-orange), 0.5);
}

/* Dashboard specific background */
.dashboard-bg {
  background: linear-gradient(135deg, hsl(195, 100%, 70%), hsl(30, 100%, 70%)); /* Sky Blue to Orange */
  background-size: 200% 200%;
  animation: dashboardGradientAnimation 20s ease infinite;
}

@keyframes dashboardGradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* --- Enhanced Intro Animation Styles --- */
.intro-bg-animation {
  background: linear-gradient(-45deg, 
    hsl(240, 90%, 30%), /* Deep Blue */
    hsl(270, 85%, 40%), /* Royal Purple */
    hsl(210, 95%, 45%), /* Bright Sky Blue */
    hsl(250, 90%, 50%)  /* Vibrant Indigo */
  );
  background-size: 600% 600%; /* Increased size for smoother, larger waves */
  animation: introWavyGradient 20s ease infinite, fadeInBg 1s ease-out forwards;
}

@keyframes introWavyGradient {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 0% 100%; }
  75% { background-position: 50% 0%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInBg {
  from { opacity: 0; }
  to { opacity: 1; }
}

.intro-logo-filter {
  filter: drop-shadow(0 0 12px hsl(210, 100%, 75%, 0.7)) 
          drop-shadow(0 0 24px hsl(270, 100%, 75%, 0.5))
          drop-shadow(0 0 3px hsl(0, 0%, 100%, 0.3)); /* Subtle white highlight */
}

@keyframes pulseSubtle {
  0%, 100% { transform: scale(1); opacity: 0.9; }
  50% { transform: scale(1.03); opacity: 1; }
}
.animate-pulseSubtle {
  animation: pulseSubtle 4s infinite ease-in-out;
}


.intro-text-program {
  background: linear-gradient(75deg, hsl(200, 100%, 80%), hsl(280, 100%, 85%), hsl(200, 100%, 80%));
  background-size: 200% auto; /* For animated shine/movement */
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 1px 2px hsla(0, 0%, 0%, 0.1),
               0 0 15px hsl(220, 100%, 80%, 0.5),
               0 0 30px hsl(270, 100%, 80%, 0.3);
  animation: shineText 8s infinite linear;
}

.intro-subtitle-program {
  color: hsl(220, 80%, 90%);
  text-shadow: 0 0 8px hsl(220, 80%, 85%, 0.4);
}

.intro-text-programmer {
  background: linear-gradient(75deg, hsl(190, 100%, 85%), hsl(310, 100%, 90%), hsl(190, 100%, 85%));
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 1px 2px hsla(0, 0%, 0%, 0.1),
               0 0 18px hsl(270, 100%, 88%, 0.6),
               0 0 35px hsl(290, 100%, 88%, 0.4);
  animation: shineText 9s infinite linear reverse; /* Slightly different timing/direction */
}

@keyframes shineText {
  to {
    background-position: 200% center;
  }
}

.intro-text-secondary {
  color: hsl(210, 70%, 85%);
  opacity: 0.9;
}
.intro-text-copyright, .intro-text-version {
  color: hsl(0, 0%, 95%);
  opacity: 0.8;
  text-shadow: 0 0 3px hsla(0, 0%, 0%, 0.2);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes fadeInScaleUpSlightly { /* Renamed from fadeInScaleUp for clarity */
  from { opacity: 0; transform: scale(0.95) translateY(15px); }
  to { opacity: 1; transform: scale(1) translateY(0); }
}
@keyframes slideInFromBottomSmooth { /* Renamed for clarity */
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInDelayed {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fadeIn { animation: fadeIn 1s ease-out forwards; }
.animate-fadeInScaleUpSlightly { animation: fadeInScaleUpSlightly 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; }
.animate-slideInFromBottomSmooth { animation: slideInFromBottomSmooth 1.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; }
.animate-fadeInDelayed { animation: fadeInDelayed 1s ease-out 0.5s forwards; } /* Added delay */


/* --- Enhanced Outro Animation Styles --- */
.outro-bg-animation {
  background: linear-gradient(135deg, 
    hsl(270, 80%, 35%), /* Deep Purple */
    hsl(0, 75%, 40%),   /* Rich Red */
    hsl(280, 85%, 30%), /* Dark Magenta */
    hsl(10, 70%, 38%)   /* Warm Orange-Brown */
  );
  background-size: 500% 500%;
  animation: outroWavyGradient 18s ease infinite, fadeInBg 1s ease-out forwards;
}

@keyframes outroWavyGradient {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 0% 100%; }
  75% { background-position: 50% 0%; }
  100% { background-position: 0% 50%; }
}

.outro-logo-filter {
  filter: drop-shadow(0 0 15px hsl(0, 100%, 80%, 0.6)) 
          drop-shadow(0 0 30px hsl(270, 100%, 80%, 0.6))
          drop-shadow(0 0 4px hsl(0, 0%, 100%, 0.25));
}
.outro-text-main {
  background: linear-gradient(60deg, hsl(0, 100%, 92%), hsl(270, 100%, 92%), hsl(0, 100%, 92%));
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 1px 2px hsla(0,0%,0%,0.1), 0 0 20px hsl(0, 0%, 100%, 0.5);
  animation: shineText 7s infinite linear;
}
.outro-text-secondary {
  color: hsl(0, 0%, 96%);
  text-shadow: 0 0 10px hsl(0, 0%, 95%, 0.35);
}
.outro-text-subtle {
  color: hsl(0, 0%, 90%);
  opacity: 0.9;
  text-shadow: 0 0 3px hsla(0,0%,0%,0.15);
}

.animate-fadeInScaleUpSlow { 
  animation: fadeInScaleUpSlightly 2.2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards; 
}
