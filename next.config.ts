
import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  output: 'export',
  // Add basePath and assetPrefix for proper static file serving
  basePath: '',
  assetPrefix: './',
  trailingSlash: true,
  typescript: {
    // Do not ignore build errors to catch potential issues
    ignoreBuildErrors: false,
  },
  eslint: {
    // Do not ignore linting errors during builds
    ignoreDuringBuilds: false,
  },
  images: {
    unoptimized: true, // Required for static export if using next/image
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
